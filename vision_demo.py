#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DobotVisionStudio 工件识别演示程序 (集成硬件连接)

这个演示程序展示了如何使用DobotVisionStudio进行工件识别的完整流程：
1. 初始化视觉系统
2. 连接实际视觉硬件 (基于plus.py的协议)
3. 加载配置和模板
4. 执行工件检测
5. 坐标转换和机器人控制
6. 结果可视化和保存
"""

import cv2
import numpy as np
import json
import time
import os
import socket
import queue
import logging
from datetime import datetime
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading

from vision_workpiece_detector import VisionWorkpieceDetector
from custom_workpiece_learner import CustomWorkpieceLearner

# === 硬件连接配置 (来自plus.py) ===
VISION_HARDWARE_IP = "*************"
VISION_SERVER_PORT = 6005      # 接收视觉硬件数据
VISION_TRIGGER_PORT = 6006     # 发送触发命令
VISION_TRIGGER_CMD = "TRIGGER"

class VisionDemoGUI:
    """视觉检测演示GUI界面 (集成硬件连接)"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DobotVisionStudio 工件识别演示 (硬件集成版)")
        self.root.geometry("1400x900")

        # 初始化检测器
        self.detector = VisionWorkpieceDetector("vision_config.json")

        # 初始化自定义学习器
        self.custom_learner = CustomWorkpieceLearner()

        # 当前图像和结果
        self.current_image = None
        self.detection_results = []
        self.template_image = None

        # ROI选择相关
        self.roi_start_point = None
        self.roi_end_point = None
        self.roi_rect = None
        self.is_drawing_roi = False
        self.roi_enabled = False
        self.roi_locked = False  # ROI锁定状态
        self.stable_roi = None   # 稳定的ROI区域
        self.roi_confidence_threshold = 0.6  # ROI识别置信度阈值

        # 硬件连接相关
        self.vision_queue = queue.Queue()
        self.hardware_connected = False
        self.vision_listener_thread = None
        self.is_running = True

        self.setup_ui()
        self.setup_logging()
        self.start_hardware_listener()
        self.process_hardware_queue()

        # 更新自定义模型状态
        self.update_custom_model_status()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图像显示")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 设置控制面板
        self.setup_control_panel(control_frame)
        
        # 设置图像显示
        self.setup_image_display(image_frame)
        
    def setup_control_panel(self, parent):
        """设置控制面板"""
        # 文件操作
        file_frame = ttk.LabelFrame(parent, text="文件操作")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(file_frame, text="加载图像", command=self.load_image).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="加载模板", command=self.load_template).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="保存结果", command=self.save_results).pack(fill=tk.X, pady=2)
        
        # 检测算法选择
        algo_frame = ttk.LabelFrame(parent, text="检测算法")
        algo_frame.pack(fill=tk.X, pady=5)

        self.algo_var = tk.StringVar(value="custom")  # 默认选择自定义学习
        ttk.Radiobutton(algo_frame, text="圆形检测", variable=self.algo_var, value="circle").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="模板匹配", variable=self.algo_var, value="template").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="边缘特征", variable=self.algo_var, value="edge").pack(anchor=tk.W)

        # 突出显示自定义学习选项
        custom_frame = ttk.Frame(algo_frame)
        custom_frame.pack(fill=tk.X, pady=2)
        ttk.Radiobutton(custom_frame, text="🎯 自定义工件识别", variable=self.algo_var, value="custom",
                       style="Accent.TRadiobutton").pack(anchor=tk.W)
        ttk.Label(custom_frame, text="   (识别您训练的工件)", font=("Arial", 8),
                 foreground="blue").pack(anchor=tk.W)
        
        # 参数设置
        param_frame = ttk.LabelFrame(parent, text="参数设置")
        param_frame.pack(fill=tk.X, pady=5)
        
        # 圆形检测参数
        ttk.Label(param_frame, text="最小半径:").pack(anchor=tk.W)
        self.min_radius_var = tk.IntVar(value=10)
        ttk.Scale(param_frame, from_=5, to=50, variable=self.min_radius_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="最大半径:").pack(anchor=tk.W)
        self.max_radius_var = tk.IntVar(value=200)
        ttk.Scale(param_frame, from_=50, to=500, variable=self.max_radius_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # 模板匹配参数
        ttk.Label(param_frame, text="匹配阈值:").pack(anchor=tk.W)
        self.match_threshold_var = tk.DoubleVar(value=0.7)
        ttk.Scale(param_frame, from_=0.1, to=1.0, variable=self.match_threshold_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # ROI选择控制
        roi_frame = ttk.LabelFrame(parent, text="检测区域选择")
        roi_frame.pack(fill=tk.X, pady=5)

        self.roi_enabled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(roi_frame, text="启用区域选择",
                       variable=self.roi_enabled_var,
                       command=self.toggle_roi_mode).pack(anchor=tk.W, pady=2)

        ttk.Button(roi_frame, text="绘制检测区域", command=self.start_roi_selection).pack(fill=tk.X, pady=1)

        # ROI控制按钮
        roi_control_frame = ttk.Frame(roi_frame)
        roi_control_frame.pack(fill=tk.X, pady=1)
        ttk.Button(roi_control_frame, text="锁定区域", command=self.lock_roi).pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
        ttk.Button(roi_control_frame, text="清除区域", command=self.clear_roi).pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)

        self.roi_status_var = tk.StringVar(value="未选择区域")
        ttk.Label(roi_frame, textvariable=self.roi_status_var, font=("Arial", 8)).pack(pady=1)

        # 识别参数调整
        param_adj_frame = ttk.LabelFrame(roi_frame, text="识别参数")
        param_adj_frame.pack(fill=tk.X, pady=2)

        ttk.Label(param_adj_frame, text="置信度阈值:", font=("Arial", 8)).pack(anchor=tk.W)
        self.confidence_threshold_var = tk.DoubleVar(value=0.6)
        confidence_scale = ttk.Scale(param_adj_frame, from_=0.1, to=1.0,
                                   variable=self.confidence_threshold_var,
                                   orient=tk.HORIZONTAL)
        confidence_scale.pack(fill=tk.X, padx=2)

        self.confidence_label_var = tk.StringVar(value="0.60")
        ttk.Label(param_adj_frame, textvariable=self.confidence_label_var,
                 font=("Arial", 8)).pack()

        # 绑定置信度更新
        confidence_scale.configure(command=self.update_confidence_display)

        # 执行按钮
        action_frame = ttk.LabelFrame(parent, text="执行操作")
        action_frame.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame, text="开始检测", command=self.start_detection).pack(fill=tk.X, pady=2)
        ttk.Button(action_frame, text="清除结果", command=self.clear_results).pack(fill=tk.X, pady=2)

        # 测试功能
        test_frame = ttk.LabelFrame(parent, text="测试功能")
        test_frame.pack(fill=tk.X, pady=5)

        ttk.Button(test_frame, text="测试图像显示", command=self.test_image_display).pack(fill=tk.X, pady=1)
        ttk.Button(test_frame, text="创建测试图像", command=self.create_test_image).pack(fill=tk.X, pady=1)

        # 自定义学习功能
        custom_frame = ttk.LabelFrame(parent, text="🎯 自定义工件识别")
        custom_frame.pack(fill=tk.X, pady=5)

        # 添加使用说明
        help_label = ttk.Label(custom_frame, text="识别您训练的工件:", font=("Arial", 9, "bold"),
                              foreground="blue")
        help_label.pack(anchor=tk.W, pady=2)

        steps_text = """1. 打开学习界面训练工件
2. 选择'自定义工件识别'算法
3. 使用ROI框选工件区域
4. 开始检测获得工件信息"""
        steps_label = ttk.Label(custom_frame, text=steps_text, font=("Arial", 8),
                               justify=tk.LEFT)
        steps_label.pack(anchor=tk.W, pady=2)

        ttk.Button(custom_frame, text="🚀 打开学习界面", command=self.open_learning_gui).pack(fill=tk.X, pady=1)
        ttk.Button(custom_frame, text="🤖 训练模型", command=self.train_custom_model).pack(fill=tk.X, pady=1)

        self.custom_model_status_var = tk.StringVar(value="模型未训练")
        status_label = ttk.Label(custom_frame, textvariable=self.custom_model_status_var,
                                font=("Arial", 8))
        status_label.pack(pady=1)
        
        # 硬件连接控制 (基于plus.py协议)
        hardware_frame = ttk.LabelFrame(parent, text="视觉硬件连接")
        hardware_frame.pack(fill=tk.X, pady=5)

        self.hardware_status_var = tk.StringVar(value="等待连接")
        ttk.Label(hardware_frame, textvariable=self.hardware_status_var).pack()

        # 自动检测开关
        self.auto_detect_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(hardware_frame, text="自动检测硬件图像",
                       variable=self.auto_detect_var).pack(anchor=tk.W, pady=2)

        ttk.Button(hardware_frame, text="触发拍照", command=self.trigger_hardware_capture).pack(fill=tk.X, pady=2)
        ttk.Button(hardware_frame, text="重连硬件", command=self.reconnect_hardware).pack(fill=tk.X, pady=2)

        # TCP服务器控制
        tcp_frame = ttk.LabelFrame(parent, text="TCP服务器")
        tcp_frame.pack(fill=tk.X, pady=5)

        self.tcp_status_var = tk.StringVar(value="未启动")
        ttk.Label(tcp_frame, textvariable=self.tcp_status_var).pack()

        ttk.Button(tcp_frame, text="启动服务器", command=self.start_tcp_server).pack(fill=tk.X, pady=2)
        ttk.Button(tcp_frame, text="停止服务器", command=self.stop_tcp_server).pack(fill=tk.X, pady=2)
        
        # 结果显示
        result_frame = ttk.LabelFrame(parent, text="检测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.result_text = tk.Text(result_frame, height=10, width=30)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def setup_image_display(self, parent):
        """设置图像显示区域"""
        # 图像显示画布
        self.canvas = tk.Canvas(parent, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 状态栏
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        self.coord_var = tk.StringVar(value="坐标: (0, 0)")
        ttk.Label(status_frame, textvariable=self.coord_var).pack(side=tk.RIGHT)
        
        # 绑定鼠标事件
        self.canvas.bind("<Motion>", self.on_mouse_move)
        self.canvas.bind("<Button-1>", self.on_mouse_click)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_release)
        
    def setup_logging(self):
        """设置日志记录"""
        self.detector.logger.addHandler(self.TextHandler(self.result_text))
        
    class TextHandler(logging.Handler):
        """自定义日志处理器，将日志输出到Text控件"""
        def __init__(self, text_widget):
            super().__init__()
            self.text_widget = text_widget
            self.setLevel(logging.INFO)  # 设置日志级别

        def emit(self, record):
            try:
                msg = f"[{datetime.now().strftime('%H:%M:%S')}] {record.getMessage()}\n"
                self.text_widget.insert(tk.END, msg)
                self.text_widget.see(tk.END)
            except Exception:
                pass  # 忽略GUI更新错误
            
    def load_image(self):
        """加载图像文件 - 使用可靠的加载机制"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 使用PIL先验证和加载，然后转换为OpenCV格式
                with Image.open(file_path) as img:
                    # 验证图像
                    img.verify()

                # 重新打开进行处理
                with Image.open(file_path) as img:
                    # 转换为RGB模式
                    if img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 转换为numpy数组，然后转为OpenCV格式
                    img_array = np.array(img)
                    self.current_image = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

                if self.current_image is not None:
                    self.display_image(self.current_image)
                    self.status_var.set(f"已加载图像: {os.path.basename(file_path)}")
                    self.log_message(f"✅ 图像加载成功: {file_path}")
                else:
                    messagebox.showerror("错误", "无法加载图像文件")

            except Exception as e:
                error_msg = f"加载图像失败: {e}"
                messagebox.showerror("错误", error_msg)
                self.log_message(f"❌ {error_msg}")
                
    def load_template(self):
        """加载模板图像"""
        file_path = filedialog.askopenfilename(
            title="选择模板图像",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.template_image = cv2.imread(file_path)
                if self.template_image is not None:
                    self.status_var.set(f"已加载模板: {os.path.basename(file_path)}")
                else:
                    messagebox.showerror("错误", "无法加载模板文件")
            except Exception as e:
                messagebox.showerror("错误", f"加载模板失败: {e}")
                
    def start_detection(self):
        """开始检测"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先加载图像")
            return
            
        # 更新检测参数
        self.update_detection_params()
        
        # 在后台线程中执行检测
        threading.Thread(target=self.run_detection, daemon=True).start()
        
    def update_detection_params(self):
        """更新检测参数"""
        # 更新圆形检测参数
        self.detector.circle_params.update({
            'min_radius': self.min_radius_var.get(),
            'max_radius': self.max_radius_var.get()
        })
        
        # 更新模板匹配参数
        self.detector.template_params.update({
            'match_threshold': self.match_threshold_var.get()
        })
        
    def run_detection(self):
        """执行检测算法"""
        try:
            self.status_var.set("检测中...")

            # 获取检测图像（可能是ROI区域）
            detection_image, roi_offset = self.get_detection_image()

            algo_type = self.algo_var.get()

            if algo_type == "circle":
                results = self.detector.detect_circular_workpiece(detection_image)
            elif algo_type == "template":
                if self.template_image is None:
                    messagebox.showwarning("警告", "请先加载模板图像")
                    return
                results = self.detector.detect_template_workpiece(detection_image, self.template_image)
            elif algo_type == "edge":
                results = self.detector.detect_edge_features(detection_image)
            elif algo_type == "custom":
                # 检查是否有训练好的模型
                model_info = self.custom_learner.get_model_info()
                if not model_info['is_trained'] and not model_info['model_exists']:
                    self.root.after(0, lambda: messagebox.showwarning(
                        "模型未训练",
                        "请先训练自定义工件模型！\n\n步骤：\n1. 点击'打开学习界面'\n2. 加载工件图像并标注\n3. 输入工件信息\n4. 学习工件并训练模型"
                    ))
                    return
                results = self.run_custom_recognition_with_roi(detection_image)
            else:
                results = []

            # 如果使用了ROI，需要调整坐标
            if roi_offset:
                results = self.adjust_results_for_roi(results, roi_offset)

            self.detection_results = results

            # 在主线程中更新UI
            self.root.after(0, self.update_detection_results)

        except Exception as e:
            error_msg = f"检测失败: {e}"
            self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", msg))
            
    def update_detection_results(self):
        """更新检测结果显示"""
        if self.detection_results:
            # 在图像上绘制检测结果
            result_image = self.draw_detection_results(self.current_image.copy())
            self.display_image(result_image)
            
            # 显示检测统计
            self.status_var.set(f"检测完成，找到 {len(self.detection_results)} 个工件")
            
            # 输出坐标信息
            for i, result in enumerate(self.detection_results):
                coord_str = self.detector.convert_to_robot_coordinates(result)
                self.result_text.insert(tk.END, f"工件 {i+1}: {coord_str}\n")
                
        else:
            self.status_var.set("检测完成，未找到工件")
            
    def draw_detection_results(self, image):
        """在图像上绘制检测结果"""
        for result in self.detection_results:
            x, y = int(result['center_x']), int(result['center_y'])
            
            if result['type'] == 'circle':
                r = int(result['radius'])
                cv2.circle(image, (x, y), r, (0, 255, 0), 2)
                cv2.circle(image, (x, y), 2, (0, 0, 255), 3)
                
            elif result['type'] == 'template':
                w, h = int(result.get('width', 50)), int(result.get('height', 50))
                cv2.rectangle(image, (x-w//2, y-h//2), (x+w//2, y+h//2), (255, 0, 0), 2)
                
            elif result['type'] == 'edge_feature':
                bbox = result.get('bbox', {})
                if bbox:
                    cv2.rectangle(image, (bbox['x'], bbox['y']),
                                (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']),
                                (0, 255, 255), 2)

            elif result['type'] == 'custom_workpiece':
                # 绘制自定义工件识别结果
                bbox = result.get('bbox', [])
                if len(bbox) == 4:
                    x, y, w, h = bbox
                    cv2.rectangle(image, (x, y), (x + w, y + h), (255, 0, 255), 3)

                    # 添加工件名称标签
                    workpiece_name = result.get('workpiece_name', 'Unknown')
                    cv2.putText(image, workpiece_name, (x, y-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            
            # 添加文本标注
            confidence = result.get('confidence', 0)
            cv2.putText(image, f"{confidence:.2f}", (x-20, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                       
        return image
        
    def display_image(self, image):
        """在画布上显示图像 - 学习plus.py的实现"""
        try:
            # 转换颜色空间
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 获取画布实际尺寸，使用update_idletasks确保尺寸正确
            self.canvas.update_idletasks()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 如果画布尺寸还未初始化，使用默认尺寸
            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width = 640
                canvas_height = 480
                self.log_message(f"⚠️ 使用默认画布尺寸: {canvas_width}x{canvas_height}")

            # 调整图像大小以适应画布（学习plus.py的thumbnail方法）
            h, w = image_rgb.shape[:2]
            scale = min(canvas_width/w, canvas_height/h)
            new_w, new_h = int(w*scale), int(h*scale)

            # 使用高质量缩放
            image_resized = cv2.resize(image_rgb, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)

            # 转换为PIL图像
            pil_image = Image.fromarray(image_resized)

            # 创建PhotoImage并保持引用（关键！）
            self.photo = ImageTk.PhotoImage(pil_image)

            # 清除画布并显示图像
            self.canvas.delete("all")
            self.canvas.create_image(canvas_width//2, canvas_height//2,
                                   image=self.photo, anchor=tk.CENTER)

            # 强制更新显示
            self.canvas.update()

            # 如果有ROI选择，重新绘制ROI矩形
            if self.roi_rect and self.roi_enabled_var.get():
                self.redraw_roi_rectangle()

            self.log_message(f"✅ 图像显示成功 ({new_w}x{new_h})")

        except Exception as e:
            self.log_message(f"❌ 图像显示失败: {e}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
                                   
    def clear_results(self):
        """清除检测结果"""
        self.detection_results = []
        if self.current_image is not None:
            self.display_image(self.current_image)
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("结果已清除")
        
    def save_results(self):
        """保存检测结果"""
        if not self.detection_results or self.current_image is None:
            messagebox.showwarning("警告", "没有检测结果可保存")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="保存检测结果",
            defaultextension=".jpg",
            filetypes=[("JPEG文件", "*.jpg"), ("PNG文件", "*.png"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                result_image = self.draw_detection_results(self.current_image.copy())
                cv2.imwrite(file_path, result_image)
                
                # 同时保存JSON格式的检测数据
                json_path = file_path.rsplit('.', 1)[0] + '.json'
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(self.detection_results, f, ensure_ascii=False, indent=2)
                    
                messagebox.showinfo("成功", f"结果已保存到:\n{file_path}\n{json_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
                
    def start_tcp_server(self):
        """启动TCP服务器"""
        try:
            self.detector.start_tcp_server()
            self.tcp_status_var.set("服务器运行中")
        except Exception as e:
            messagebox.showerror("错误", f"启动TCP服务器失败: {e}")
            
    def stop_tcp_server(self):
        """停止TCP服务器"""
        try:
            self.detector.stop_tcp_server()
            self.tcp_status_var.set("服务器已停止")
        except Exception as e:
            messagebox.showerror("错误", f"停止TCP服务器失败: {e}")

    # === 硬件连接方法 (基于plus.py) ===

    def start_hardware_listener(self):
        """启动硬件监听线程"""
        self.vision_listener_thread = threading.Thread(target=self.hardware_listener_worker, daemon=True)
        self.vision_listener_thread.start()

    def hardware_listener_worker(self):
        """硬件监听工作线程"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT))
                s.listen()
                self.log_message(f"👂 视觉硬件监听已启动，端口: {VISION_SERVER_PORT}")

                while self.is_running:
                    try:
                        conn, addr = s.accept()
                        self.hardware_connected = True
                        self.root.after(0, lambda: self.hardware_status_var.set(f"已连接: {addr[0]}"))
                        self.log_message(f"🤝 视觉硬件已连接: {addr}")

                        with conn:
                            while self.is_running:
                                data = conn.recv(1024)
                                if not data:
                                    break
                                message = data.decode('utf-8').strip()
                                self.vision_queue.put(message)

                    except Exception as e:
                        if self.is_running:
                            self.log_message(f"❌ 硬件连接错误: {e}")

                    finally:
                        self.hardware_connected = False
                        self.root.after(0, lambda: self.hardware_status_var.set("连接断开"))

            except OSError as e:
                self.log_message(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")

    def process_hardware_queue(self):
        """处理硬件数据队列"""
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log_message(f"📩 收到硬件数据: {message}")
                self.parse_hardware_message(message)
        except queue.Empty:
            pass

        # 继续处理队列
        self.root.after(100, self.process_hardware_queue)

    def parse_hardware_message(self, message):
        """解析硬件消息 - 适配实际硬件数据格式"""
        try:
            # 调试：打印原始消息的详细信息
            self.log_message(f"🔍 原始消息长度: {len(message)}, 内容: '{message}'")
            self.log_message(f"🔍 消息字节表示: {[ord(c) for c in message[:50]]}")  # 只显示前50个字符

            # 清理消息：移除不可见字符和异常字符
            cleaned_message = ''.join(c for c in message if c.isprintable() and ord(c) < 127)
            cleaned_message = cleaned_message.strip()

            if cleaned_message != message:
                self.log_message(f"🧹 清理后消息: '{cleaned_message}'")

            # 检查消息是否为空或只包含异常字符
            if not cleaned_message or cleaned_message in ['?', '??', '???', '????', '?????', '??????', '???????']:
                self.log_message(f"⚠️ 收到无效消息，跳过处理")
                return

            # 检查是否为纯图像路径格式（实际硬件发送的格式）
            if cleaned_message.endswith('.jpg') or cleaned_message.endswith('.png'):
                self.log_message(f"📷 检测到图像路径格式: {cleaned_message}")
                self.handle_image_only_message(cleaned_message)
                return

            # 标准格式: "坐标数据;图像路径" 或 "X,Y,R;image_path"
            parts = cleaned_message.split(';')
            self.log_message(f"🔍 分割后的部分: {parts}")

            if len(parts) >= 2:
                coord_data = parts[0].strip()
                image_path = parts[-1].strip()

                self.log_message(f"🔍 坐标数据: '{coord_data}', 图像路径: '{image_path}'")

                # 验证图像路径
                if not image_path or image_path in ['?', '??', '???', '????', '?????', '??????', '???????']:
                    self.log_message(f"⚠️ 图像路径无效: '{image_path}'，跳过处理")
                    return

                # 解析坐标数据
                if coord_data and coord_data != 'black':  # 跳过 'black' 这种无效数据
                    coord_parts = coord_data.split(',')
                    if len(coord_parts) >= 2:
                        try:
                            robot_x = float(coord_parts[0])
                            robot_y = float(coord_parts[1])
                            robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0

                            self.log_message(f"📍 硬件检测坐标: X={robot_x:.1f}, Y={robot_y:.1f}, R={robot_r:.1f}")

                            # 创建检测结果
                            hardware_result = {
                                'type': 'hardware_detection',
                                'center_x': robot_x,
                                'center_y': robot_y,
                                'angle': robot_r,
                                'confidence': 1.0,
                                'timestamp': datetime.now().isoformat(),
                                'source': 'vision_hardware'
                            }

                            self.detection_results = [hardware_result]
                        except ValueError as e:
                            self.log_message(f"⚠️ 坐标数据格式错误: '{coord_data}', 错误: {e}")

                # 加载并显示图像
                self.handle_image_path(image_path)

            elif len(parts) == 1 and not parts[0]:
                # 空消息，跳过
                self.log_message(f"⚠️ 收到空消息，跳过处理")
            else:
                self.log_message(f"⚠️ 消息格式不正确，期望包含';'分隔符: '{cleaned_message}'")

        except Exception as e:
            self.log_message(f"❌ 解析硬件消息失败: {e}")
            import traceback
            self.log_message(f"❌ 详细错误信息: {traceback.format_exc()}")

    def handle_image_only_message(self, image_path):
        """处理只包含图像路径的消息"""
        self.log_message(f"📸 处理图像路径: {image_path}")

        # 直接加载并显示图像
        self.handle_image_path(image_path)

        # 如果启用了自动检测，对图像进行检测
        if self.auto_detect_var.get():
            self.log_message(f"🔍 自动检测模式：对接收的图像进行检测")
            # 延迟执行检测，确保图像已加载
            self.root.after(500, self.auto_detect_current_image)
        else:
            self.log_message(f"ℹ️ 自动检测已关闭，仅显示图像")

    def handle_image_path(self, image_path):
        """处理图像路径"""
        if os.path.exists(image_path):
            self.load_hardware_image(image_path)
        else:
            self.log_message(f"❌ 硬件图像文件不存在: {image_path}")
            # 尝试检查目录是否存在
            image_dir = os.path.dirname(image_path)
            if os.path.exists(image_dir):
                self.log_message(f"📁 图像目录存在: {image_dir}")
                # 列出目录中的文件
                try:
                    files = os.listdir(image_dir)
                    recent_files = [f for f in files if f.endswith('.jpg') or f.endswith('.png')]
                    if recent_files:
                        self.log_message(f"📁 目录中的图像文件: {recent_files[:5]}")  # 只显示前5个
                        # 尝试加载最新的图像文件
                        latest_file = max([os.path.join(image_dir, f) for f in recent_files],
                                        key=os.path.getctime)
                        self.log_message(f"🔄 尝试加载最新图像: {latest_file}")
                        self.load_hardware_image(latest_file)
                except Exception as e:
                    self.log_message(f"❌ 无法列出目录文件: {e}")
            else:
                self.log_message(f"❌ 图像目录不存在: {image_dir}")

    def auto_detect_current_image(self):
        """对当前图像进行自动检测"""
        if self.current_image is not None:
            self.log_message(f"🎯 对硬件图像执行自动检测...")
            self.start_detection()
        else:
            self.log_message(f"⚠️ 没有可检测的图像")

    def load_hardware_image(self, image_path):
        """加载硬件传来的图像 - 学习plus.py的实现"""
        max_retries = 5
        retry_delay = 0.2

        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    # 第一步：验证图像文件完整性（学习plus.py）
                    with Image.open(image_path) as img:
                        img.verify()

                    # 第二步：重新打开并加载图像
                    with Image.open(image_path) as img:
                        # 转换为RGB模式确保兼容性
                        if img.mode != 'RGB':
                            img = img.convert('RGB')

                        # 转换为numpy数组，然后转为OpenCV格式
                        img_array = np.array(img)
                        self.current_image = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

                    if self.current_image is not None:
                        # 显示图像（保持ROI稳定）
                        self.display_image_stable(self.current_image)

                        self.log_message(f"✅ 硬件图像加载成功 (尝试 {attempt + 1})")

                        # 如果启用了自动检测且ROI已锁定，自动进行检测
                        if self.auto_detect_var.get() and self.roi_locked and self.stable_roi:
                            self.root.after(100, self.auto_detect_with_stable_roi)

                        return
                    else:
                        self.log_message(f"   - 第 {attempt + 1} 次尝试：图像数据为空")

                except (IOError, SyntaxError) as e:
                    self.log_message(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...")
                    time.sleep(retry_delay)
                except PermissionError:
                    self.log_message(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...")
                    time.sleep(retry_delay)
                except Exception as e:
                    self.log_message(f"❌ 加载硬件图像失败: {e}")
                    return
            else:
                self.log_message(f"❌ 找不到图像文件: {image_path}")
                return

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                time.sleep(retry_delay)

        self.log_message(f"❌ 硬件图像加载失败：{max_retries} 次尝试后仍无法读取")

    def trigger_hardware_capture(self):
        """触发硬件拍照"""
        self.log_message("📸 发送硬件拍照触发指令...")
        try:
            # 检查硬件IP和端口配置
            self.log_message(f"🔗 尝试连接到: {VISION_HARDWARE_IP}:{VISION_TRIGGER_PORT}")

            with socket.create_connection((VISION_HARDWARE_IP, VISION_TRIGGER_PORT), timeout=5) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"
                s.sendall(cmd_to_send.encode('utf-8'))
                self.log_message("✅ 硬件触发指令发送成功")

                # 尝试接收响应（如果硬件有响应的话）
                try:
                    s.settimeout(2)  # 设置接收超时
                    response = s.recv(1024)
                    if response:
                        self.log_message(f"📨 硬件响应: {response.decode('utf-8', errors='ignore').strip()}")
                except socket.timeout:
                    self.log_message("ℹ️ 硬件无响应（正常情况）")
                except Exception:
                    pass  # 忽略接收错误

        except socket.timeout:
            self.log_message(f"❌ 硬件触发失败: 连接超时 ({VISION_HARDWARE_IP}:{VISION_TRIGGER_PORT})")
            self.log_message("💡 请检查：1) 硬件设备是否开启 2) IP地址是否正确 3) 网络连接是否正常")
        except ConnectionRefusedError:
            self.log_message(f"❌ 硬件触发失败: 连接被拒绝 ({VISION_HARDWARE_IP}:{VISION_TRIGGER_PORT})")
            self.log_message("💡 请检查：1) 硬件设备是否启动 2) 触发端口是否正确 3) 防火墙设置")
        except Exception as e:
            self.log_message(f"❌ 硬件触发失败: {e}")
            self.log_message(f"💡 错误类型: {type(e).__name__}")

    def reconnect_hardware(self):
        """重新连接硬件"""
        self.log_message("🔄 尝试重新连接视觉硬件...")
        self.hardware_connected = False
        self.hardware_status_var.set("重连中...")

        # 重启监听线程
        if self.vision_listener_thread and self.vision_listener_thread.is_alive():
            self.log_message("重启硬件监听线程...")

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {message}"

        # 添加到结果文本框
        self.result_text.insert(tk.END, log_text + "\n")
        self.result_text.see(tk.END)

        # 同时输出到控制台
        print(log_text)

    def on_mouse_move(self, event):
        """鼠标移动事件"""
        self.coord_var.set(f"坐标: ({event.x}, {event.y})")

        # 如果正在绘制ROI，更新矩形
        if self.is_drawing_roi and self.roi_start_point:
            self.update_roi_rectangle(event.x, event.y)

    def on_mouse_click(self, event):
        """鼠标点击事件"""
        if self.roi_enabled_var.get() and self.current_image is not None:
            # 开始绘制ROI
            self.roi_start_point = (event.x, event.y)
            self.is_drawing_roi = True
            self.clear_roi_rectangle()
            self.log_message(f"🎯 开始绘制检测区域，起点: ({event.x}, {event.y})")

    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if self.is_drawing_roi and self.roi_start_point:
            self.update_roi_rectangle(event.x, event.y)

    def on_mouse_release(self, event):
        """鼠标释放事件"""
        if self.is_drawing_roi and self.roi_start_point:
            self.roi_end_point = (event.x, event.y)
            self.is_drawing_roi = False
            self.finalize_roi_selection()
            self.log_message(f"✅ 检测区域绘制完成，终点: ({event.x}, {event.y})")

    def create_test_image(self):
        """创建测试图像 - 学习plus.py的测试图像创建"""
        try:
            # 创建一个包含圆形的测试图像
            img = np.zeros((480, 640, 3), dtype=np.uint8)
            img.fill(50)  # 深灰色背景

            # 绘制几个圆形作为测试工件
            cv2.circle(img, (200, 200), 50, (255, 255, 255), -1)  # 白色圆形
            cv2.circle(img, (400, 300), 30, (200, 200, 200), -1)  # 灰色圆形
            cv2.circle(img, (500, 150), 40, (180, 180, 180), -1)  # 另一个灰色圆形

            # 添加一些噪声
            noise = np.random.randint(0, 30, img.shape, dtype=np.uint8)
            img = cv2.add(img, noise)

            # 保存测试图像
            test_image_path = "test_workpiece_demo.jpg"
            cv2.imwrite(test_image_path, img)

            # 加载并显示测试图像
            self.current_image = img
            self.display_image(img)

            self.log_message(f"✅ 创建并显示测试图像: {test_image_path}")
            self.status_var.set("测试图像已创建")

        except Exception as e:
            self.log_message(f"❌ 创建测试图像失败: {e}")

    def test_image_display(self):
        """测试图像显示功能"""
        try:
            if self.current_image is not None:
                # 测试当前图像显示
                self.display_image(self.current_image)
                self.log_message("✅ 图像显示测试完成")
            else:
                # 创建一个简单的测试图像
                test_img = np.zeros((300, 400, 3), dtype=np.uint8)
                test_img[:] = (100, 150, 200)  # 蓝色背景

                # 添加一些文字
                cv2.putText(test_img, "Image Display Test", (50, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.putText(test_img, "Vision Demo Working!", (50, 200),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                self.current_image = test_img
                self.display_image(test_img)
                self.log_message("✅ 图像显示测试完成 - 使用测试图像")

            self.status_var.set("图像显示测试完成")

        except Exception as e:
            self.log_message(f"❌ 图像显示测试失败: {e}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")

    def open_learning_gui(self):
        """打开学习界面"""
        try:
            from workpiece_learning_gui import WorkpieceLearningGUI
            learning_app = WorkpieceLearningGUI()
            learning_app.run()
        except Exception as e:
            self.log_message(f"❌ 打开学习界面失败: {e}")
            messagebox.showerror("错误", f"打开学习界面失败: {e}")

    def train_custom_model(self):
        """训练自定义模型"""
        def train_worker():
            try:
                self.custom_model_status_var.set("正在训练...")
                success = self.custom_learner.train_model()

                if success:
                    self.root.after(0, lambda: self.custom_model_status_var.set("模型已训练"))
                    self.root.after(0, lambda: self.log_message("✅ 自定义模型训练成功"))
                else:
                    self.root.after(0, lambda: self.custom_model_status_var.set("训练失败"))
                    self.root.after(0, lambda: self.log_message("❌ 自定义模型训练失败"))

            except Exception as e:
                self.root.after(0, lambda: self.custom_model_status_var.set("训练失败"))
                self.root.after(0, lambda: self.log_message(f"❌ 自定义模型训练失败: {e}"))

        # 在后台线程中训练
        threading.Thread(target=train_worker, daemon=True).start()

    def run_custom_recognition(self):
        """运行自定义识别"""
        try:
            # 使用自定义学习器进行识别
            result = self.custom_learner.recognize_workpiece(self.current_image)

            if result['success']:
                # 转换为标准格式
                custom_result = {
                    'type': 'custom_workpiece',
                    'center_x': float(result['center_x']),
                    'center_y': float(result['center_y']),
                    'confidence': float(result['confidence']),
                    'workpiece_name': result['workpiece_name'],
                    'workpiece_info': result['workpiece_info'],
                    'bbox': result['bbox'],
                    'timestamp': result['timestamp']
                }

                self.log_message(f"🎯 自定义识别成功: {result['workpiece_name']} (置信度: {result['confidence']:.3f})")

                # 显示详细信息
                if result['workpiece_info']:
                    info = result['workpiece_info']
                    self.log_message(f"   类型: {info.get('type', 'N/A')}")
                    self.log_message(f"   规格: {info.get('specifications', 'N/A')}")
                    self.log_message(f"   材料: {info.get('material', 'N/A')}")
                    self.log_message(f"   供应商: {info.get('supplier', 'N/A')}")

                return [custom_result]
            else:
                self.log_message(f"❌ 自定义识别失败: {result.get('error', '未知错误')}")
                return []

        except Exception as e:
            self.log_message(f"❌ 自定义识别异常: {e}")
            return []

    def update_custom_model_status(self):
        """更新自定义模型状态"""
        try:
            model_info = self.custom_learner.get_model_info()
            if model_info['is_trained']:
                self.custom_model_status_var.set("模型已训练")
            elif model_info['model_exists']:
                self.custom_model_status_var.set("模型文件存在")
            else:
                self.custom_model_status_var.set("模型未训练")
        except Exception as e:
            self.log_message(f"❌ 更新模型状态失败: {e}")

    # === ROI选择相关方法 ===

    def toggle_roi_mode(self):
        """切换ROI模式"""
        if self.roi_enabled_var.get():
            self.log_message("🎯 已启用区域选择模式")
            self.roi_status_var.set("请在图像上绘制检测区域")
        else:
            self.log_message("❌ 已关闭区域选择模式")
            self.clear_roi()

    def start_roi_selection(self):
        """开始ROI选择"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先加载图像")
            return

        self.roi_enabled_var.set(True)
        self.log_message("🎯 请在图像上拖拽鼠标绘制检测区域")
        self.roi_status_var.set("请拖拽鼠标绘制区域")

    def clear_roi(self):
        """清除ROI选择"""
        self.roi_start_point = None
        self.roi_end_point = None
        self.roi_rect = None
        self.is_drawing_roi = False
        self.roi_locked = False
        self.stable_roi = None
        self.clear_roi_rectangle()
        self.roi_status_var.set("未选择区域")
        self.log_message("🗑️ 已清除检测区域和锁定状态")

        # 重新显示原图
        if self.current_image is not None:
            self.display_image(self.current_image)

    def clear_roi_rectangle(self):
        """清除画布上的ROI矩形"""
        # 删除之前的ROI矩形
        self.canvas.delete("roi_rect")

    def update_roi_rectangle(self, x, y):
        """更新ROI矩形显示"""
        if self.roi_start_point:
            self.clear_roi_rectangle()
            # 绘制新的矩形
            self.canvas.create_rectangle(
                self.roi_start_point[0], self.roi_start_point[1],
                x, y,
                outline="red", width=2, tags="roi_rect"
            )

    def finalize_roi_selection(self):
        """完成ROI选择"""
        if self.roi_start_point and self.roi_end_point:
            # 计算ROI矩形坐标
            x1, y1 = self.roi_start_point
            x2, y2 = self.roi_end_point

            # 确保坐标正确（左上角和右下角）
            roi_x = min(x1, x2)
            roi_y = min(y1, y2)
            roi_w = abs(x2 - x1)
            roi_h = abs(y2 - y1)

            if roi_w > 10 and roi_h > 10:  # 最小尺寸检查
                self.roi_rect = (roi_x, roi_y, roi_w, roi_h)
                self.roi_status_var.set(f"区域: {roi_w}x{roi_h}")
                self.log_message(f"✅ 检测区域已设置: ({roi_x}, {roi_y}, {roi_w}, {roi_h})")
            else:
                self.log_message("⚠️ 检测区域太小，请重新绘制")
                self.clear_roi()

    def get_detection_image(self):
        """获取用于检测的图像（可能是ROI区域）"""
        if not self.roi_enabled_var.get() or not self.roi_rect or self.current_image is None:
            # 没有启用ROI或没有选择ROI，返回完整图像
            return self.current_image, None

        # 计算图像坐标系中的ROI
        roi_image_coords = self.canvas_to_image_coords(self.roi_rect)
        if roi_image_coords is None:
            self.log_message("⚠️ ROI坐标转换失败，使用完整图像")
            return self.current_image, None

        x, y, w, h = roi_image_coords

        # 确保坐标在图像范围内
        img_h, img_w = self.current_image.shape[:2]
        x = max(0, min(x, img_w - 1))
        y = max(0, min(y, img_h - 1))
        w = min(w, img_w - x)
        h = min(h, img_h - y)

        if w <= 0 or h <= 0:
            self.log_message("⚠️ ROI区域无效，使用完整图像")
            return self.current_image, None

        # 提取ROI区域
        roi_image = self.current_image[y:y+h, x:x+w]
        self.log_message(f"🎯 使用ROI区域进行检测: ({x}, {y}, {w}, {h})")

        return roi_image, (x, y)

    def canvas_to_image_coords(self, canvas_roi):
        """将画布坐标转换为图像坐标"""
        if self.current_image is None:
            return None

        # 获取画布和图像尺寸
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        img_h, img_w = self.current_image.shape[:2]

        # 计算缩放比例（与display_image中的逻辑一致）
        scale = min(canvas_width/img_w, canvas_height/img_h)
        display_w, display_h = int(img_w * scale), int(img_h * scale)

        # 计算图像在画布中的偏移
        offset_x = (canvas_width - display_w) // 2
        offset_y = (canvas_height - display_h) // 2

        # 转换ROI坐标
        canvas_x, canvas_y, canvas_w, canvas_h = canvas_roi

        # 调整为相对于图像的坐标
        rel_x = canvas_x - offset_x
        rel_y = canvas_y - offset_y

        # 转换为图像坐标
        img_x = int(rel_x / scale)
        img_y = int(rel_y / scale)
        img_w_roi = int(canvas_w / scale)
        img_h_roi = int(canvas_h / scale)

        return (img_x, img_y, img_w_roi, img_h_roi)

    def adjust_results_for_roi(self, results, roi_offset):
        """调整检测结果坐标以适应ROI偏移"""
        if not roi_offset:
            return results

        offset_x, offset_y = roi_offset
        adjusted_results = []

        for result in results:
            adjusted_result = result.copy()
            adjusted_result['center_x'] += offset_x
            adjusted_result['center_y'] += offset_y

            # 如果有边界框，也需要调整
            if 'bbox' in result and isinstance(result['bbox'], list) and len(result['bbox']) >= 2:
                bbox = result['bbox'].copy()
                bbox[0] += offset_x  # x坐标
                bbox[1] += offset_y  # y坐标
                adjusted_result['bbox'] = bbox

            adjusted_results.append(adjusted_result)

        return adjusted_results

    def run_custom_recognition_with_roi(self, image):
        """使用ROI图像运行自定义识别"""
        try:
            self.log_message(f"🔍 开始自定义工件识别...")

            # 直接使用自定义学习器识别ROI图像
            result = self.custom_learner.recognize_workpiece(image)

            if result['success']:
                confidence = float(result['confidence'])

                # 检查置信度是否达到阈值
                if confidence < self.roi_confidence_threshold:
                    self.log_message(f"⚠️ 识别置信度过低: {result['workpiece_name']} (置信度: {confidence:.3f} < {self.roi_confidence_threshold:.2f})")
                    return []

                # 转换为标准格式
                custom_result = {
                    'type': 'custom_workpiece',
                    'center_x': float(result['center_x']),
                    'center_y': float(result['center_y']),
                    'confidence': confidence,
                    'workpiece_name': result['workpiece_name'],
                    'workpiece_info': result['workpiece_info'],
                    'bbox': result['bbox'],
                    'timestamp': result['timestamp']
                }

                self.log_message(f"🎯 识别成功: {result['workpiece_name']} (置信度: {confidence:.3f})")

                # 显示详细信息
                if result['workpiece_info']:
                    info = result['workpiece_info']
                    self.log_message(f"   📋 工件详细信息:")
                    self.log_message(f"     名称: {info.get('name', 'N/A')}")
                    self.log_message(f"     类型: {info.get('type', 'N/A')}")
                    self.log_message(f"     规格: {info.get('specifications', 'N/A')}")
                    self.log_message(f"     材料: {info.get('material', 'N/A')}")
                    self.log_message(f"     供应商: {info.get('supplier', 'N/A')}")
                    if info.get('notes'):
                        self.log_message(f"     备注: {info.get('notes')}")

                return [custom_result]
            else:
                self.log_message(f"❌ 自定义识别失败: {result.get('error', '未知错误')}")
                return []

        except Exception as e:
            self.log_message(f"❌ ROI自定义识别异常: {e}")
            return []

    def display_image_stable(self, image):
        """稳定的图像显示，保持ROI不变"""
        try:
            # 保存当前ROI状态
            current_roi = self.roi_rect if self.roi_locked else None

            # 显示图像
            self.display_image(image)

            # 如果有锁定的ROI，恢复显示
            if current_roi and self.roi_locked:
                self.roi_rect = current_roi
                self.redraw_roi_rectangle()

        except Exception as e:
            self.log_message(f"❌ 稳定图像显示失败: {e}")

    def auto_detect_with_stable_roi(self):
        """使用稳定ROI进行自动检测"""
        if self.current_image is not None and self.stable_roi:
            try:
                # 使用稳定的ROI进行检测
                old_roi = self.roi_rect
                self.roi_rect = self.stable_roi

                # 执行检测
                self.start_detection()

                # 恢复ROI
                self.roi_rect = old_roi

            except Exception as e:
                self.log_message(f"❌ 稳定ROI检测失败: {e}")

    def lock_roi(self):
        """锁定当前ROI区域"""
        if self.roi_rect:
            self.stable_roi = self.roi_rect
            self.roi_locked = True
            self.roi_status_var.set(f"区域已锁定: {self.roi_rect[2]}x{self.roi_rect[3]}")
            self.log_message(f"🔒 ROI区域已锁定: {self.stable_roi}")
        else:
            messagebox.showwarning("警告", "请先绘制检测区域")

    def update_confidence_display(self, value):
        """更新置信度显示"""
        confidence = float(value)
        self.confidence_label_var.set(f"{confidence:.2f}")
        self.roi_confidence_threshold = confidence

    def redraw_roi_rectangle(self):
        """重新绘制ROI矩形（在图像重新显示后）"""
        if self.roi_rect:
            x, y, w, h = self.roi_rect
            self.canvas.create_rectangle(
                x, y, x + w, y + h,
                outline="red", width=2, tags="roi_rect"
            )

    def run(self):
        """运行GUI"""
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """窗口关闭事件处理"""
        self.log_message("🔄 正在关闭程序...")

        # 停止硬件监听
        self.is_running = False

        # 停止TCP服务器
        try:
            self.detector.stop_tcp_server()
        except:
            pass

        # 关闭窗口
        self.root.destroy()


if __name__ == "__main__":
    # 创建演示应用
    app = VisionDemoGUI()
    
    try:
        app.run()
    except KeyboardInterrupt:
        print("程序退出")
    finally:
        # 清理资源
        app.detector.stop_tcp_server()
