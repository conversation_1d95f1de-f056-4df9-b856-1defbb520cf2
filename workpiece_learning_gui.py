#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工件学习标注GUI界面
支持图像加载、区域标注、信息输入和模型训练

主要功能:
1. 图像加载和显示
2. 鼠标拖拽标注工件区域
3. 工件信息输入和编辑
4. 模型训练和管理
5. 工件数据库管理
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import cv2
import numpy as np
import os
from PIL import Image, ImageTk
import threading
from custom_workpiece_learner import CustomWorkpieceLearner

class WorkpieceLearningGUI:
    """工件学习标注GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("工件学习标注系统")
        self.root.geometry("1200x800")
        
        # 初始化学习器
        self.learner = CustomWorkpieceLearner()
        
        # 当前图像和标注
        self.current_image = None
        self.current_image_cv = None
        self.annotation_rects = []  # 标注矩形列表
        self.current_rect = None    # 当前正在绘制的矩形
        self.start_x = None
        self.start_y = None

        # 批量处理相关
        self.image_list = []        # 图像文件列表
        self.current_image_index = 0  # 当前图像索引
        
        # 画布缩放比例
        self.scale_factor = 1.0
        
        self.setup_ui()
        self.update_workpiece_list()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 右侧图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图像标注区域")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.setup_control_panel(control_frame)
        self.setup_image_area(image_frame)
        
    def setup_control_panel(self, parent):
        """设置控制面板"""
        # 图像操作
        image_frame = ttk.LabelFrame(parent, text="图像操作")
        image_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(image_frame, text="加载单张图像", command=self.load_image).pack(fill=tk.X, pady=2)
        ttk.Button(image_frame, text="加载图像文件夹", command=self.load_image_folder).pack(fill=tk.X, pady=2)
        ttk.Button(image_frame, text="清除标注", command=self.clear_annotations).pack(fill=tk.X, pady=2)

        # 图像导航
        nav_frame = ttk.Frame(image_frame)
        nav_frame.pack(fill=tk.X, pady=2)

        ttk.Button(nav_frame, text="上一张", command=self.prev_image).pack(side=tk.LEFT, padx=2)
        ttk.Button(nav_frame, text="下一张", command=self.next_image).pack(side=tk.LEFT, padx=2)

        self.image_info_var = tk.StringVar(value="无图像")
        ttk.Label(nav_frame, textvariable=self.image_info_var).pack(side=tk.LEFT, padx=10)
        
        # 工件信息输入
        info_frame = ttk.LabelFrame(parent, text="工件信息")
        info_frame.pack(fill=tk.X, pady=5)
        
        # 工件名称
        ttk.Label(info_frame, text="工件名称:").pack(anchor=tk.W)
        self.name_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.name_var).pack(fill=tk.X, pady=2)
        
        # 工件类型
        ttk.Label(info_frame, text="工件类型:").pack(anchor=tk.W)
        self.type_var = tk.StringVar()
        type_combo = ttk.Combobox(info_frame, textvariable=self.type_var)
        type_combo['values'] = ('螺丝', '螺母', '垫片', '轴承', '齿轮', '电子元件', '其他')
        type_combo.pack(fill=tk.X, pady=2)
        
        # 规格
        ttk.Label(info_frame, text="规格:").pack(anchor=tk.W)
        self.spec_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.spec_var).pack(fill=tk.X, pady=2)
        
        # 材料
        ttk.Label(info_frame, text="材料:").pack(anchor=tk.W)
        self.material_var = tk.StringVar()
        material_combo = ttk.Combobox(info_frame, textvariable=self.material_var)
        material_combo['values'] = ('不锈钢', '碳钢', '铝合金', '铜', '塑料', '其他')
        material_combo.pack(fill=tk.X, pady=2)
        
        # 供应商
        ttk.Label(info_frame, text="供应商:").pack(anchor=tk.W)
        self.supplier_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.supplier_var).pack(fill=tk.X, pady=2)
        
        # 备注
        ttk.Label(info_frame, text="备注:").pack(anchor=tk.W)
        self.notes_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.notes_var).pack(fill=tk.X, pady=2)
        
        # 学习操作
        learn_frame = ttk.LabelFrame(parent, text="学习操作")
        learn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(learn_frame, text="学习当前标注", command=self.learn_current_annotation).pack(fill=tk.X, pady=2)
        ttk.Button(learn_frame, text="添加样本", command=self.add_sample).pack(fill=tk.X, pady=2)
        
        # 模型管理
        model_frame = ttk.LabelFrame(parent, text="模型管理")
        model_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(model_frame, text="训练模型", command=self.train_model).pack(fill=tk.X, pady=2)
        ttk.Button(model_frame, text="测试识别", command=self.test_recognition).pack(fill=tk.X, pady=2)
        
        self.model_status_var = tk.StringVar(value="模型未训练")
        ttk.Label(model_frame, textvariable=self.model_status_var).pack(pady=2)
        
        # 工件数据库
        db_frame = ttk.LabelFrame(parent, text="工件数据库")
        db_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 工件列表
        self.workpiece_tree = ttk.Treeview(db_frame, columns=('name', 'type', 'count'), show='headings', height=8)
        self.workpiece_tree.heading('name', text='名称')
        self.workpiece_tree.heading('type', text='类型')
        self.workpiece_tree.heading('count', text='样本数')
        self.workpiece_tree.column('name', width=80)
        self.workpiece_tree.column('type', width=60)
        self.workpiece_tree.column('count', width=50)
        
        scrollbar = ttk.Scrollbar(db_frame, orient=tk.VERTICAL, command=self.workpiece_tree.yview)
        self.workpiece_tree.configure(yscrollcommand=scrollbar.set)
        
        self.workpiece_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 数据库操作按钮
        db_btn_frame = ttk.Frame(db_frame)
        db_btn_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(db_btn_frame, text="删除", command=self.delete_workpiece).pack(side=tk.LEFT, padx=2)
        ttk.Button(db_btn_frame, text="编辑", command=self.edit_workpiece).pack(side=tk.LEFT, padx=2)
        ttk.Button(db_btn_frame, text="刷新", command=self.update_workpiece_list).pack(side=tk.LEFT, padx=2)
        
    def setup_image_area(self, parent):
        """设置图像显示区域"""
        # 图像画布
        self.canvas = tk.Canvas(parent, bg="white", cursor="crosshair")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_mouse_press)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_release)
        self.canvas.bind("<Motion>", self.on_mouse_move)
        
        # 状态栏
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_var = tk.StringVar(value="请加载图像")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        self.coord_var = tk.StringVar(value="坐标: (0, 0)")
        ttk.Label(status_frame, textvariable=self.coord_var).pack(side=tk.RIGHT)
        
        # 操作提示
        help_text = """操作说明：
1. 加载图像（单张或文件夹）
2. 鼠标拖拽标注工件区域
3. 可在同一图像标注多个相同工件
4. 填写工件信息（所有标注共用）
5. 点击'学习当前标注'
6. 切换到下一张图像继续标注"""
        ttk.Label(status_frame, text=help_text, font=("Arial", 8), justify=tk.LEFT).pack(side=tk.LEFT, padx=20)
        
    def load_image(self):
        """加载图像"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 使用OpenCV加载图像
                self.current_image_cv = cv2.imread(file_path)
                if self.current_image_cv is None:
                    messagebox.showerror("错误", "无法加载图像文件")
                    return
                
                # 转换为RGB用于显示
                image_rgb = cv2.cvtColor(self.current_image_cv, cv2.COLOR_BGR2RGB)
                
                # 调整图像大小以适应画布
                self.display_image(image_rgb)
                
                # 清除之前的标注
                self.clear_annotations()
                
                self.status_var.set(f"已加载图像: {file_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载图像失败: {e}")

    def load_image_folder(self):
        """加载图像文件夹"""

        folder_path = filedialog.askdirectory(title="选择包含图像的文件夹")

        if folder_path:
            try:
                # 支持的图像格式
                image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

                # 获取文件夹中的所有图像文件
                self.image_list = []
                for filename in os.listdir(folder_path):
                    if os.path.splitext(filename.lower())[1] in image_extensions:
                        full_path = os.path.join(folder_path, filename)
                        self.image_list.append(full_path)

                if self.image_list:
                    # 按文件名排序
                    self.image_list.sort()
                    self.current_image_index = 0

                    # 加载第一张图像
                    self.load_current_image()

                    messagebox.showinfo("成功", f"找到 {len(self.image_list)} 张图像")
                else:
                    messagebox.showwarning("警告", "文件夹中没有找到支持的图像文件")

            except Exception as e:
                messagebox.showerror("错误", f"加载文件夹失败: {e}")

    def load_current_image(self):
        """加载当前索引的图像"""
        if not self.image_list or self.current_image_index >= len(self.image_list):
            return

        file_path = self.image_list[self.current_image_index]

        try:
            # 使用OpenCV加载图像
            self.current_image_cv = cv2.imread(file_path)
            if self.current_image_cv is None:
                messagebox.showerror("错误", f"无法加载图像文件: {file_path}")
                return

            # 转换为RGB用于显示
            image_rgb = cv2.cvtColor(self.current_image_cv, cv2.COLOR_BGR2RGB)

            # 调整图像大小以适应画布
            self.display_image(image_rgb)

            # 清除之前的标注
            self.clear_annotations()

            # 更新状态信息
            filename = os.path.basename(file_path)
            self.status_var.set(f"已加载图像: {filename}")
            self.image_info_var.set(f"{self.current_image_index + 1}/{len(self.image_list)}: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"加载图像失败: {e}")

    def prev_image(self):
        """上一张图像"""
        if self.image_list and self.current_image_index > 0:
            self.current_image_index -= 1
            self.load_current_image()

    def next_image(self):
        """下一张图像"""
        if self.image_list and self.current_image_index < len(self.image_list) - 1:
            self.current_image_index += 1
            self.load_current_image()
    
    def display_image(self, image_rgb):
        """显示图像到画布"""
        try:
            # 获取画布尺寸
            self.canvas.update_idletasks()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width = 800
                canvas_height = 600
            
            # 计算缩放比例
            h, w = image_rgb.shape[:2]
            scale_w = canvas_width / w
            scale_h = canvas_height / h
            self.scale_factor = min(scale_w, scale_h, 1.0)  # 不放大，只缩小
            
            # 调整图像大小
            new_w = int(w * self.scale_factor)
            new_h = int(h * self.scale_factor)
            
            image_resized = cv2.resize(image_rgb, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_resized)
            self.current_image = ImageTk.PhotoImage(pil_image)
            
            # 显示图像
            self.canvas.delete("all")
            self.canvas.create_image(canvas_width//2, canvas_height//2, 
                                   image=self.current_image, anchor=tk.CENTER)
            
            # 保存图像在画布中的位置信息
            self.image_offset_x = (canvas_width - new_w) // 2
            self.image_offset_y = (canvas_height - new_h) // 2
            self.image_width = new_w
            self.image_height = new_h
            
        except Exception as e:
            print(f"显示图像失败: {e}")
    
    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if self.current_image_cv is None:
            return
        
        # 检查是否在图像区域内
        if (self.image_offset_x <= event.x <= self.image_offset_x + self.image_width and
            self.image_offset_y <= event.y <= self.image_offset_y + self.image_height):
            
            self.start_x = event.x
            self.start_y = event.y
            
            # 创建新的矩形
            self.current_rect = self.canvas.create_rectangle(
                self.start_x, self.start_y, self.start_x, self.start_y,
                outline="red", width=2
            )
    
    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if self.current_rect and self.start_x and self.start_y:
            # 限制在图像区域内
            x = max(self.image_offset_x, min(event.x, self.image_offset_x + self.image_width))
            y = max(self.image_offset_y, min(event.y, self.image_offset_y + self.image_height))
            
            # 更新矩形
            self.canvas.coords(self.current_rect, self.start_x, self.start_y, x, y)
    
    def on_mouse_release(self, event):
        """鼠标释放事件"""
        if self.current_rect and self.start_x and self.start_y:
            # 计算最终矩形坐标
            end_x = max(self.image_offset_x, min(event.x, self.image_offset_x + self.image_width))
            end_y = max(self.image_offset_y, min(event.y, self.image_offset_y + self.image_height))
            
            # 确保矩形有最小尺寸
            if abs(end_x - self.start_x) > 10 and abs(end_y - self.start_y) > 10:
                # 转换为原始图像坐标
                orig_x1 = int((self.start_x - self.image_offset_x) / self.scale_factor)
                orig_y1 = int((self.start_y - self.image_offset_y) / self.scale_factor)
                orig_x2 = int((end_x - self.image_offset_x) / self.scale_factor)
                orig_y2 = int((end_y - self.image_offset_y) / self.scale_factor)
                
                # 确保坐标顺序正确
                x1, x2 = min(orig_x1, orig_x2), max(orig_x1, orig_x2)
                y1, y2 = min(orig_y1, orig_y2), max(orig_y1, orig_y2)
                
                # 保存标注信息
                annotation = {
                    'canvas_rect': self.current_rect,
                    'bbox': (x1, y1, x2 - x1, y2 - y1),  # x, y, width, height
                    'canvas_coords': (self.start_x, self.start_y, end_x, end_y)
                }
                self.annotation_rects.append(annotation)
                
                self.status_var.set(f"标注区域: ({x1}, {y1}, {x2-x1}, {y2-y1})")
            else:
                # 删除太小的矩形
                self.canvas.delete(self.current_rect)
            
            self.current_rect = None
            self.start_x = None
            self.start_y = None
    
    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if self.current_image_cv is not None:
            # 转换为原始图像坐标
            orig_x = int((event.x - self.image_offset_x) / self.scale_factor)
            orig_y = int((event.y - self.image_offset_y) / self.scale_factor)
            self.coord_var.set(f"坐标: ({orig_x}, {orig_y})")

    def clear_annotations(self):
        """清除所有标注"""
        for annotation in self.annotation_rects:
            self.canvas.delete(annotation['canvas_rect'])
        self.annotation_rects = []
        self.status_var.set("标注已清除")

    def learn_current_annotation(self):
        """学习当前标注的工件"""
        if not self.annotation_rects:
            messagebox.showwarning("警告", "请先标注工件区域")
            return

        if not self.name_var.get().strip():
            messagebox.showwarning("警告", "请输入工件名称")
            return

        try:
            # 获取工件信息
            workpiece_info = {
                'name': self.name_var.get().strip(),
                'type': self.type_var.get().strip(),
                'specifications': self.spec_var.get().strip(),
                'material': self.material_var.get().strip(),
                'supplier': self.supplier_var.get().strip(),
                'notes': self.notes_var.get().strip()
            }

            # 学习每个标注的工件
            learned_count = 0
            for annotation in self.annotation_rects:
                bbox = annotation['bbox']
                workpiece_id = self.learner.learn_workpiece(
                    self.current_image_cv, bbox, workpiece_info
                )
                if workpiece_id:
                    learned_count += 1

            if learned_count > 0:
                messagebox.showinfo("成功", f"成功学习 {learned_count} 个工件样本")
                self.update_workpiece_list()
                self.clear_annotations()
                self.clear_input_fields()
            else:
                messagebox.showerror("错误", "学习工件失败")

        except Exception as e:
            messagebox.showerror("错误", f"学习工件失败: {e}")

    def add_sample(self):
        """为已有工件添加样本"""
        if not self.annotation_rects:
            messagebox.showwarning("警告", "请先标注工件区域")
            return

        # 获取工件列表
        workpiece_list = self.learner.get_workpiece_list()
        if not workpiece_list:
            messagebox.showwarning("警告", "没有已学习的工件，请先学习新工件")
            return

        # 选择工件
        workpiece_names = [w['name'] for w in workpiece_list]
        selected_name = simpledialog.askstring(
            "选择工件",
            f"请选择要添加样本的工件:\n{', '.join(workpiece_names)}"
        )

        if not selected_name:
            return

        # 查找工件ID
        workpiece_id = None
        for w in workpiece_list:
            if w['name'] == selected_name:
                workpiece_id = w['id']
                break

        if not workpiece_id:
            messagebox.showerror("错误", "未找到指定工件")
            return

        try:
            # 添加样本
            added_count = 0
            for annotation in self.annotation_rects:
                bbox = annotation['bbox']
                if self.learner.add_workpiece_sample(workpiece_id, self.current_image_cv, bbox):
                    added_count += 1

            if added_count > 0:
                messagebox.showinfo("成功", f"成功添加 {added_count} 个样本")
                self.update_workpiece_list()
                self.clear_annotations()
            else:
                messagebox.showerror("错误", "添加样本失败")

        except Exception as e:
            messagebox.showerror("错误", f"添加样本失败: {e}")

    def train_model(self):
        """训练模型"""
        def train_worker():
            try:
                self.model_status_var.set("正在训练模型...")
                success = self.learner.train_model()

                if success:
                    self.root.after(0, lambda: self.model_status_var.set("模型训练成功"))
                    self.root.after(0, lambda: messagebox.showinfo("成功", "模型训练完成"))
                else:
                    self.root.after(0, lambda: self.model_status_var.set("模型训练失败"))
                    self.root.after(0, lambda: messagebox.showerror("错误", "模型训练失败"))

            except Exception as e:
                self.root.after(0, lambda: self.model_status_var.set("模型训练失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"模型训练失败: {e}"))

        # 在后台线程中训练
        threading.Thread(target=train_worker, daemon=True).start()

    def test_recognition(self):
        """测试识别"""
        if self.current_image_cv is None:
            messagebox.showwarning("警告", "请先加载图像")
            return

        try:
            # 执行识别
            result = self.learner.recognize_workpiece(self.current_image_cv)

            if result['success']:
                # 显示识别结果
                info = result['workpiece_info']
                message = f"识别结果:\n"
                message += f"工件名称: {result['workpiece_name']}\n"
                message += f"置信度: {result['confidence']:.3f}\n"
                message += f"位置: ({result['center_x']}, {result['center_y']})\n"

                if info:
                    message += f"类型: {info.get('type', 'N/A')}\n"
                    message += f"规格: {info.get('specifications', 'N/A')}\n"
                    message += f"材料: {info.get('material', 'N/A')}\n"

                messagebox.showinfo("识别结果", message)

                # 在图像上绘制识别结果
                self.draw_recognition_result(result)

            else:
                messagebox.showerror("识别失败", result.get('error', '未知错误'))

        except Exception as e:
            messagebox.showerror("错误", f"识别失败: {e}")

    def draw_recognition_result(self, result):
        """在图像上绘制识别结果"""
        try:
            bbox = result['bbox']
            x, y, w, h = bbox

            # 转换为画布坐标
            canvas_x1 = int(x * self.scale_factor + self.image_offset_x)
            canvas_y1 = int(y * self.scale_factor + self.image_offset_y)
            canvas_x2 = int((x + w) * self.scale_factor + self.image_offset_x)
            canvas_y2 = int((y + h) * self.scale_factor + self.image_offset_y)

            # 绘制识别框
            self.canvas.create_rectangle(
                canvas_x1, canvas_y1, canvas_x2, canvas_y2,
                outline="green", width=3, tags="recognition"
            )

            # 绘制标签
            label = f"{result['workpiece_name']} ({result['confidence']:.2f})"
            self.canvas.create_text(
                canvas_x1, canvas_y1 - 10,
                text=label, fill="green", anchor=tk.SW, tags="recognition"
            )

            # 5秒后清除识别结果
            self.root.after(5000, lambda: self.canvas.delete("recognition"))

        except Exception as e:
            print(f"绘制识别结果失败: {e}")

    def update_workpiece_list(self):
        """更新工件列表"""
        try:
            # 清除现有项目
            for item in self.workpiece_tree.get_children():
                self.workpiece_tree.delete(item)

            # 添加工件项目
            workpiece_list = self.learner.get_workpiece_list()
            for workpiece in workpiece_list:
                self.workpiece_tree.insert('', 'end', values=(
                    workpiece['name'],
                    workpiece['type'],
                    workpiece['learn_count']
                ))

            # 更新模型状态
            model_info = self.learner.get_model_info()
            if model_info['is_trained']:
                self.model_status_var.set("模型已训练")
            elif model_info['model_exists']:
                self.model_status_var.set("模型文件存在，未加载")
            else:
                self.model_status_var.set("模型未训练")

        except Exception as e:
            print(f"更新工件列表失败: {e}")

    def delete_workpiece(self):
        """删除选中的工件"""
        selection = self.workpiece_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的工件")
            return

        # 获取选中的工件信息
        item = selection[0]
        workpiece_name = self.workpiece_tree.item(item)['values'][0]

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除工件 '{workpiece_name}' 吗？"):
            try:
                # 查找工件ID
                workpiece_list = self.learner.get_workpiece_list()
                workpiece_id = None
                for w in workpiece_list:
                    if w['name'] == workpiece_name:
                        workpiece_id = w['id']
                        break

                if workpiece_id and self.learner.delete_workpiece(workpiece_id):
                    messagebox.showinfo("成功", "工件删除成功")
                    self.update_workpiece_list()
                else:
                    messagebox.showerror("错误", "删除工件失败")

            except Exception as e:
                messagebox.showerror("错误", f"删除工件失败: {e}")

    def edit_workpiece(self):
        """编辑选中的工件信息"""
        selection = self.workpiece_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的工件")
            return

        # 获取选中的工件信息
        item = selection[0]
        workpiece_name = self.workpiece_tree.item(item)['values'][0]

        # 查找工件详细信息
        workpiece_list = self.learner.get_workpiece_list()
        workpiece_info = None
        workpiece_id = None
        for w in workpiece_list:
            if w['name'] == workpiece_name:
                workpiece_info = w['info']
                workpiece_id = w['id']
                break

        if not workpiece_info:
            messagebox.showerror("错误", "未找到工件信息")
            return

        # 创建编辑对话框
        self.show_edit_dialog(workpiece_id, workpiece_info)

    def show_edit_dialog(self, workpiece_id, workpiece_info):
        """显示编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑工件信息")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 创建输入字段
        fields = {}
        field_names = [
            ('name', '工件名称'),
            ('type', '工件类型'),
            ('specifications', '规格'),
            ('material', '材料'),
            ('supplier', '供应商'),
            ('notes', '备注')
        ]

        for i, (key, label) in enumerate(field_names):
            ttk.Label(dialog, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=5)
            var = tk.StringVar(value=workpiece_info.get(key, ''))
            entry = ttk.Entry(dialog, textvariable=var, width=30)
            entry.grid(row=i, column=1, padx=5, pady=5)
            fields[key] = var

        # 按钮
        btn_frame = ttk.Frame(dialog)
        btn_frame.grid(row=len(field_names), column=0, columnspan=2, pady=10)

        def save_changes():
            try:
                new_info = {key: var.get().strip() for key, var in fields.items()}
                if self.learner.update_workpiece_info(workpiece_id, new_info):
                    messagebox.showinfo("成功", "工件信息更新成功")
                    self.update_workpiece_list()
                    dialog.destroy()
                else:
                    messagebox.showerror("错误", "更新工件信息失败")
            except Exception as e:
                messagebox.showerror("错误", f"更新失败: {e}")

        ttk.Button(btn_frame, text="保存", command=save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def clear_input_fields(self):
        """清除输入字段"""
        self.name_var.set("")
        self.type_var.set("")
        self.spec_var.set("")
        self.material_var.set("")
        self.supplier_var.set("")
        self.notes_var.set("")

    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = WorkpieceLearningGUI()
    app.run()
