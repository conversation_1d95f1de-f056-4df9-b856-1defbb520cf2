#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义工件识别功能
"""

import cv2
import numpy as np
from custom_workpiece_learner import CustomWorkpieceLearner

def test_custom_recognition():
    """测试自定义工件识别"""
    print("🔍 测试自定义工件识别功能...")
    
    # 创建学习器
    learner = CustomWorkpieceLearner()
    
    # 检查模型状态
    model_info = learner.get_model_info()
    print(f"📊 模型状态:")
    print(f"   是否已训练: {model_info['is_trained']}")
    print(f"   模型文件存在: {model_info['model_exists']}")
    print(f"   工件数量: {model_info['workpiece_count']}")
    
    if not model_info['is_trained'] and not model_info['model_exists']:
        print("❌ 没有训练好的模型！")
        print("💡 请先运行以下步骤:")
        print("   1. python workpiece_learning_gui.py")
        print("   2. 加载工件图像并标注")
        print("   3. 输入工件信息")
        print("   4. 学习工件并训练模型")
        return
    
    # 尝试加载模型
    if not learner.is_trained:
        print("🔄 加载模型...")
        if learner.load_model():
            print("✅ 模型加载成功")
        else:
            print("❌ 模型加载失败")
            return
    
    # 获取工件列表
    workpiece_list = learner.get_workpiece_list()
    print(f"\n📋 已学习的工件 ({len(workpiece_list)} 个):")
    for i, workpiece in enumerate(workpiece_list, 1):
        print(f"   {i}. {workpiece['name']} ({workpiece['type']}) - {workpiece['learn_count']} 个样本")
        info = workpiece.get('info', {})
        if info.get('specifications'):
            print(f"      规格: {info['specifications']}")
        if info.get('material'):
            print(f"      材料: {info['material']}")
    
    # 创建测试图像
    print(f"\n🖼️ 创建测试图像...")
    test_image = create_test_image()
    
    # 测试识别
    print(f"\n🎯 开始识别测试...")
    result = learner.recognize_workpiece(test_image)
    
    if result['success']:
        print(f"✅ 识别成功!")
        print(f"   工件名称: {result['workpiece_name']}")
        print(f"   置信度: {result['confidence']:.3f}")
        print(f"   位置: ({result['center_x']}, {result['center_y']})")
        
        if result['workpiece_info']:
            info = result['workpiece_info']
            print(f"   📋 详细信息:")
            for key, value in info.items():
                if value:
                    print(f"     {key}: {value}")
    else:
        print(f"❌ 识别失败: {result.get('error', '未知错误')}")
        print(f"💡 可能的原因:")
        print(f"   1. 测试图像与训练样本差异太大")
        print(f"   2. 需要更多训练样本")
        print(f"   3. 图像质量不够清晰")

def create_test_image():
    """创建简单的测试图像"""
    # 创建一个简单的测试图像
    img = np.zeros((400, 600, 3), dtype=np.uint8)
    img.fill(100)  # 灰色背景
    
    # 绘制一个简单的矩形工件
    cv2.rectangle(img, (200, 150), (400, 250), (180, 180, 180), -1)
    cv2.rectangle(img, (220, 130), (380, 150), (160, 160, 160), -1)  # 顶部
    
    # 添加一些纹理
    for i in range(5):
        y = 170 + i * 15
        cv2.line(img, (210, y), (390, y), (200, 200, 200), 1)
    
    # 保存测试图像
    cv2.imwrite("test_recognition.jpg", img)
    print(f"   测试图像已保存: test_recognition.jpg")
    
    return img

if __name__ == "__main__":
    test_custom_recognition()
