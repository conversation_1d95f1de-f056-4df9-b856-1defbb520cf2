#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试vision_demo.py的滚动功能
"""

import tkinter as tk
from tkinter import messagebox
import time
import threading

def test_scroll_functionality():
    """测试滚动功能"""
    print("正在启动vision_demo.py进行滚动功能测试...")
    
    try:
        import vision_demo
        
        # 创建应用实例
        app = vision_demo.VisionDemoGUI()
        
        # 创建测试窗口
        test_window = tk.Toplevel(app.root)
        test_window.title("滚动功能测试")
        test_window.geometry("400x300")
        
        # 添加测试说明
        instructions = """
滚动功能测试说明：

1. 主窗口左侧是控制面板
2. 控制面板包含很多功能模块
3. 请尝试以下操作：
   - 使用鼠标滚轮在控制面板上滚动
   - 拖拽右侧的滚动条
   - 检查是否能看到所有功能模块

4. 如果滚动正常工作，说明修改成功！

点击"确认测试完成"关闭程序
        """
        
        text_widget = tk.Text(test_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.insert(tk.END, instructions)
        text_widget.config(state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        def close_test():
            test_window.destroy()
            app.root.quit()
        
        tk.Button(test_window, text="确认测试完成", command=close_test, 
                 bg="lightgreen", font=("Arial", 12)).pack(pady=10)
        
        # 启动GUI
        app.run()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scroll_functionality()
