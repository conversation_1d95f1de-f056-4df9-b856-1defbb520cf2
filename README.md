# 自定义工件学习识别系统

这是一个基于机器学习的智能工件识别系统，支持用户自定义工件学习和识别。

## 🎯 系统特点

- **自定义学习**：支持任意形状工件的学习和识别
- **智能标注**：图形界面拖拽标注，操作简单
- **高精度识别**：多特征融合的机器学习算法
- **信息管理**：完整的工件属性信息管理
- **实时识别**：快速准确的工件识别和定位
- **硬件集成**：支持实际视觉硬件连接

## 📁 项目结构

```
├── vision_demo.py                    # 主界面程序
├── custom_workpiece_learner.py       # 自定义学习核心模块
├── workpiece_learning_gui.py         # 学习标注界面
├── vision_workpiece_detector.py      # 传统检测模块
├── plus.py                          # 硬件连接模块
├── vision_config.json               # 系统配置文件
├── test_custom_learning.py          # 系统测试脚本
├── workpiece_data/                  # 工件数据目录
│   ├── images/                      # 工件图像
│   ├── models/                      # 训练模型
│   └── annotations/                 # 标注数据
└── 自定义工件学习识别系统使用说明.md    # 详细使用说明
```

## 🚀 快速开始

### 1. 启动主程序
```bash
python vision_demo.py
```

### 2. 学习工件
1. 点击"打开学习界面"
2. 加载包含工件的图像
3. 拖拽标注工件区域
4. 填写工件信息
5. 点击"学习当前标注"

### 3. 训练模型
- 学习至少2种工件后
- 点击"训练模型"按钮
- 等待训练完成

### 4. 识别工件
1. 在主界面选择"自定义学习"算法
2. 加载待识别图像
3. 点击"开始检测"
4. 查看识别结果和工件信息

## 🔧 系统要求

### Python依赖
```bash
pip install opencv-python numpy scikit-learn pillow joblib tkinter
```

### 硬件要求
- Windows 10/11
- 内存：4GB以上
- 摄像头或图像文件

## 📊 功能模块

### 1. 自定义学习模块
- 图像加载和显示
- 鼠标拖拽标注
- 工件信息管理
- 特征提取和存储

### 2. 机器学习模块
- 多特征融合（SIFT、ORB、LBP、颜色、形状）
- 随机森林分类器
- 模型训练和保存
- 置信度评估

### 3. 识别检测模块
- 实时工件识别
- 自动对象检测
- 结果可视化
- 信息输出

### 4. 数据管理模块
- 工件数据库管理
- 样本增加和删除
- 模型版本控制
- 配置管理

## 🎉 使用示例

详细的使用说明请参考：[自定义工件学习识别系统使用说明.md](./自定义工件学习识别系统使用说明.md)

## 🧪 测试验证

运行测试脚本验证系统功能：
```bash
python test_custom_learning.py
```

测试包括：
- 创建测试工件图像
- 模拟学习过程
- 模型训练验证
- 识别精度测试

## 📞 技术支持

如遇问题，请检查：
1. Python依赖是否完整安装
2. 图像文件格式是否支持
3. 工件样本是否足够（每类至少2个）
4. 标注区域是否准确

## 🔄 版本信息

- **当前版本**：v1.0
- **更新日期**：2025-08-14
- **主要功能**：自定义工件学习识别系统

---

**注意**: 系统已完成清理，删除了无关文件，保留核心功能模块。
