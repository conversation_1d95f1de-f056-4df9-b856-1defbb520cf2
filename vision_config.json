{"system": {"name": "DobotVisionStudio工件识别系统", "version": "4.1.2", "description": "基于DobotVisionStudio的工件视觉识别配置"}, "communication": {"tcp_port": 8888, "vision_trigger_port": 9999, "robot_ip": "*************", "robot_port": 29999, "timeout": 5000, "hardware_connection": {"vision_hardware_ip": "*************", "vision_server_port": 6005, "vision_trigger_port": 6006, "trigger_command": "TRIGGER", "data_format": "coordinates;image_path", "coordinate_format": "X,Y,R"}}, "image_processing": {"image_save_path": "./captured_images/", "image_format": "jpg", "jpeg_quality": 80, "roi": {"x": 0, "y": 0, "width": 640, "height": 480}, "preprocessing": {"gaussian_blur_kernel": 5, "histogram_equalization": true, "noise_reduction": true}}, "coordinate_system": {"coordinate_scale": {"x": 0.1, "y": 0.1}, "origin_offset": {"x": 320, "y": 240}, "rotation_correction": 0, "pixel_to_mm_ratio": 0.1}, "detection_algorithms": {"circle_detection": {"enabled": true, "hough_params": {"dp": 1, "min_dist": 50, "param1": 50, "param2": 30, "min_radius": 10, "max_radius": 200}, "edge_threshold": 15, "edge_width": 1, "confidence_threshold": 0.6}, "template_matching": {"enabled": true, "match_threshold": 0.7, "scale_range": [0.8, 1.2], "angle_range": [-30, 30], "max_matches": 5, "template_path": "./templates/"}, "feature_matching": {"enabled": true, "min_score": 0.5, "max_match_num": 10, "use_match_all_mode": false, "max_overlap": 50}, "edge_detection": {"enabled": true, "canny_low_threshold": 50, "canny_high_threshold": 150, "min_contour_area": 100, "max_contour_area": 10000}}, "workpiece_types": {"circular_workpiece": {"name": "圆形工件", "detection_method": "circle_detection", "size_range": {"min_diameter": 20, "max_diameter": 400}, "color_range": {"lower_hsv": [0, 0, 50], "upper_hsv": [180, 255, 255]}}, "rectangular_workpiece": {"name": "矩形工件", "detection_method": "template_matching", "size_range": {"min_width": 30, "max_width": 300, "min_height": 30, "max_height": 300}, "aspect_ratio_range": [0.5, 2.0]}, "custom_workpiece": {"name": "自定义工件", "detection_method": "feature_matching", "template_file": "custom_template.jpg", "feature_points": 50}}, "quality_control": {"enabled": true, "size_tolerance": 0.05, "position_tolerance": 2.0, "angle_tolerance": 5.0, "defect_detection": {"enabled": false, "scratch_detection": true, "crack_detection": true, "contamination_detection": true}}, "logging": {"level": "INFO", "log_file": "./logs/vision_system.log", "max_log_size": "10MB", "backup_count": 5, "console_output": true}, "performance": {"max_processing_time": 1000, "parallel_processing": true, "gpu_acceleration": false, "memory_limit": "512MB"}, "calibration": {"camera_matrix": [[800, 0, 320], [0, 800, 240], [0, 0, 1]], "distortion_coefficients": [0.1, -0.2, 0, 0, 0], "calibration_date": "2024-01-01", "calibration_valid": true}, "robot_integration": {"coordinate_transformation": {"translation": [0, 0, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1]}, "pick_and_place": {"approach_height": 50, "pick_height": 10, "place_height": 10, "speed": 50}, "safety": {"workspace_limits": {"x_min": -200, "x_max": 200, "y_min": -200, "y_max": 200, "z_min": 0, "z_max": 100}, "collision_detection": true}}}