#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ROI功能的简单脚本
"""

import cv2
import numpy as np
import os

def create_test_image_with_circles():
    """创建一个包含多个圆形的测试图像"""
    # 创建一个较大的图像
    img = np.zeros((600, 800, 3), dtype=np.uint8)
    img.fill(50)  # 深灰色背景
    
    # 在不同位置绘制圆形
    circles = [
        (150, 150, 30),   # 左上区域
        (200, 180, 25),   # 左上区域
        (650, 150, 35),   # 右上区域
        (680, 200, 20),   # 右上区域
        (150, 450, 40),   # 左下区域
        (180, 480, 15),   # 左下区域
        (650, 450, 30),   # 右下区域
        (400, 300, 50),   # 中心区域 - 这是我们想要检测的
        (420, 320, 25),   # 中心区域 - 这是我们想要检测的
        (380, 280, 30),   # 中心区域 - 这是我们想要检测的
    ]
    
    for x, y, r in circles:
        # 绘制白色圆形
        cv2.circle(img, (x, y), r, (255, 255, 255), -1)
        # 添加一些边缘噪声
        cv2.circle(img, (x, y), r+2, (200, 200, 200), 1)
    
    # 添加一些随机噪声
    noise = np.random.randint(0, 20, img.shape, dtype=np.uint8)
    img = cv2.add(img, noise)
    
    # 保存测试图像
    test_image_path = "test_image_with_many_circles.jpg"
    cv2.imwrite(test_image_path, img)
    
    print(f"✅ 创建测试图像: {test_image_path}")
    print(f"   图像包含 {len(circles)} 个圆形")
    print(f"   中心区域（300-500, 200-400）包含 3 个目标圆形")
    print(f"   其他区域包含 7 个干扰圆形")
    
    return test_image_path

if __name__ == "__main__":
    # 创建测试图像
    test_image = create_test_image_with_circles()
    
    print("\n🎯 ROI功能测试说明:")
    print("1. 运行 vision_demo.py")
    print("2. 点击'加载图像'，选择刚创建的测试图像")
    print("3. 勾选'启用区域选择'")
    print("4. 在图像中心区域（包含3个圆形的区域）拖拽绘制矩形")
    print("5. 点击'开始检测'")
    print("6. 应该只检测到3个圆形，而不是全部10个")
    print("\n💡 这样就可以避免检测到不需要的圆形了！")
