# 自定义工件学习识别系统使用说明

## 🎯 系统概述

这是一个基于机器学习的自定义工件识别系统，允许用户通过简单的标注过程来训练系统识别特定的工件类型。系统会学习您提供的工件样本，并能够在后续的图像中自动识别这些工件。

## ✨ 主要特性

- **自定义学习**：支持任意形状和类型的工件学习
- **智能标注**：鼠标拖拽即可完成工件区域标注
- **信息管理**：为每个工件添加详细的属性信息
- **高精度识别**：基于多种特征融合的机器学习算法
- **实时反馈**：即时显示识别结果和置信度
- **数据管理**：完整的工件数据库管理功能

## 🚀 快速开始

### 1. 启动系统

有两种方式启动系统：

#### 方式一：主界面（推荐）
```bash
python vision_demo.py
```
在主界面中点击"打开学习界面"按钮

#### 方式二：直接启动学习界面
```bash
python workpiece_learning_gui.py
```

### 2. 学习工件

#### 步骤1：加载图像
- 点击"加载图像"按钮
- 选择包含工件的图像文件
- 支持格式：JPG, PNG, BMP

#### 步骤2：标注工件区域
- 在图像上用鼠标拖拽框选工件区域
- 可以标注多个工件
- 标注框会以红色显示

#### 步骤3：填写工件信息
- **工件名称**：必填，用于识别的主要标识
- **工件类型**：选择或输入工件类别
- **规格**：工件的具体规格参数
- **材料**：工件材质
- **供应商**：供应商信息
- **备注**：其他相关信息

#### 步骤4：学习工件
- 点击"学习当前标注"按钮
- 系统会自动提取特征并保存工件信息
- 成功后会清除标注，可以继续学习其他工件

### 3. 训练模型

- 学习至少2种不同的工件后
- 点击"训练模型"按钮
- 等待训练完成（通常几秒钟）
- 训练成功后可以进行识别

### 4. 测试识别

- 加载包含已学习工件的新图像
- 点击"测试识别"按钮
- 系统会自动检测并识别工件
- 识别结果会以绿色框显示，包含工件名称和置信度

## 📋 详细功能说明

### 工件学习功能

#### 多样本学习
- 为提高识别精度，建议为每种工件提供多个样本
- 使用"添加样本"功能为已有工件增加新样本
- 不同角度、光照条件的样本有助于提高识别鲁棒性

#### 特征提取
系统自动提取以下特征：
- **SIFT特征**：尺度不变特征，适合复杂形状
- **ORB特征**：快速特征点检测
- **LBP纹理特征**：局部二值模式，描述表面纹理
- **颜色特征**：RGB颜色直方图
- **形状特征**：面积、周长、圆形度等几何特征

### 识别算法

#### 机器学习模型
- 使用随机森林分类器
- 自动特征标准化
- 支持多类别分类
- 输出置信度评分

#### 自动检测
- 当未提供工件位置时，系统会自动检测图像中的对象
- 使用自适应阈值和轮廓分析
- 过滤噪声和小对象

### 数据管理

#### 工件数据库
- 自动保存所有学习的工件信息
- 支持工件信息编辑和删除
- 显示每个工件的样本数量

#### 模型管理
- 自动保存训练好的模型
- 支持模型重新加载
- 显示模型训练状态和信息

## 🔧 高级使用技巧

### 提高识别精度

1. **多样本学习**
   - 每种工件至少提供3-5个样本
   - 包含不同角度和光照条件的样本
   - 确保标注区域准确包含工件

2. **优质图像**
   - 使用清晰、对比度好的图像
   - 避免过度曝光或欠曝光
   - 确保工件在图像中占有足够大的区域

3. **合理分类**
   - 相似工件应归为同一类别
   - 差异明显的工件分为不同类别
   - 避免过于细分的分类

### 标注技巧

1. **精确标注**
   - 标注框应紧贴工件边缘
   - 避免包含过多背景
   - 确保包含工件的完整特征

2. **一致性**
   - 同类工件的标注方式保持一致
   - 标注区域大小尽量相近
   - 避免标注重叠区域

## 🛠️ 故障排除

### 常见问题

#### 1. 模型训练失败
- **原因**：工件类型太少或样本不足
- **解决**：至少学习2种工件，每种至少2个样本

#### 2. 识别精度低
- **原因**：样本质量差或数量不足
- **解决**：增加高质量样本，确保标注准确

#### 3. 无法检测到工件
- **原因**：图像质量差或工件与背景对比度低
- **解决**：改善图像质量，调整光照条件

#### 4. 程序启动失败
- **原因**：缺少依赖库
- **解决**：安装所需的Python库：
```bash
pip install opencv-python numpy scikit-learn pillow joblib
```

### 性能优化

1. **图像尺寸**
   - 建议图像尺寸不超过1920x1080
   - 过大的图像会影响处理速度

2. **样本数量**
   - 每种工件建议3-10个样本
   - 过多样本会增加训练时间

3. **特征选择**
   - 可以在代码中调整特征类型
   - 根据工件特点选择合适的特征组合

## 📊 系统架构

### 核心模块

1. **CustomWorkpieceLearner**：核心学习和识别引擎
2. **WorkpieceLearningGUI**：学习标注界面
3. **VisionDemoGUI**：主界面，集成所有功能

### 数据存储

- **工件数据库**：`workpiece_data/workpiece_database.json`
- **工件图像**：`workpiece_data/images/`
- **训练模型**：`workpiece_data/models/`

### 配置文件

- **视觉配置**：`vision_config.json`
- **学习参数**：在代码中可调整

## 🎉 使用示例

### 示例1：学习螺丝识别

1. 准备包含不同螺丝的图像
2. 标注每个螺丝的位置
3. 填写信息：
   - 名称：M6螺丝
   - 类型：紧固件
   - 规格：M6x20
   - 材料：不锈钢
4. 学习并训练模型
5. 测试识别效果

### 示例2：电子元件识别

1. 拍摄电路板图像
2. 标注各种电子元件
3. 分类学习：电阻、电容、芯片等
4. 训练模型进行批量识别

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看控制台输出的错误信息
2. 检查日志文件中的详细信息
3. 运行测试脚本验证系统功能：
```bash
python test_custom_learning.py
```

## 🔄 版本更新

当前版本：v1.0
- 支持基础的工件学习和识别
- 提供完整的GUI界面
- 集成到主视觉系统中

未来计划：
- 支持深度学习模型
- 增加更多特征提取算法
- 优化识别速度和精度
- 支持批量处理功能
