#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义工件学习系统测试脚本

测试功能:
1. 创建测试图像
2. 模拟工件学习
3. 模型训练
4. 工件识别测试
"""

import cv2
import numpy as np
import os
from custom_workpiece_learner import CustomWorkpieceLearner

def create_test_images():
    """创建测试图像"""
    print("🎨 创建测试图像...")
    
    # 创建测试图像目录
    test_dir = "test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建不同类型的测试工件图像
    test_images = []
    
    # 1. 圆形螺丝
    img1 = np.zeros((400, 400, 3), dtype=np.uint8)
    img1.fill(100)  # 灰色背景
    cv2.circle(img1, (200, 200), 80, (200, 200, 200), -1)  # 螺丝主体
    cv2.circle(img1, (200, 200), 15, (50, 50, 50), -1)     # 螺丝孔
    # 添加螺纹纹理
    for i in range(5):
        cv2.circle(img1, (200, 200), 60 + i*5, (150, 150, 150), 2)
    
    img1_path = os.path.join(test_dir, "screw_1.jpg")
    cv2.imwrite(img1_path, img1)
    test_images.append((img1_path, img1, (120, 120, 160, 160), {
        'name': '六角螺丝',
        'type': '紧固件',
        'specifications': 'M8x25',
        'material': '不锈钢',
        'supplier': 'ABC五金'
    }))
    
    # 2. 方形垫片
    img2 = np.zeros((400, 400, 3), dtype=np.uint8)
    img2.fill(80)
    cv2.rectangle(img2, (150, 150), (250, 250), (180, 180, 180), -1)
    cv2.rectangle(img2, (180, 180), (220, 220), (50, 50, 50), -1)  # 中心孔
    
    img2_path = os.path.join(test_dir, "washer_1.jpg")
    cv2.imwrite(img2_path, img2)
    test_images.append((img2_path, img2, (140, 140, 120, 120), {
        'name': '方形垫片',
        'type': '垫片',
        'specifications': '20x20x2',
        'material': '铜',
        'supplier': 'XYZ材料'
    }))
    
    # 3. 齿轮
    img3 = np.zeros((400, 400, 3), dtype=np.uint8)
    img3.fill(90)
    center = (200, 200)
    # 绘制齿轮
    cv2.circle(img3, center, 70, (160, 160, 160), -1)
    cv2.circle(img3, center, 20, (50, 50, 50), -1)
    # 绘制齿
    for angle in range(0, 360, 30):
        x1 = int(center[0] + 70 * np.cos(np.radians(angle)))
        y1 = int(center[1] + 70 * np.sin(np.radians(angle)))
        x2 = int(center[0] + 85 * np.cos(np.radians(angle)))
        y2 = int(center[1] + 85 * np.sin(np.radians(angle)))
        cv2.line(img3, (x1, y1), (x2, y2), (160, 160, 160), 8)
    
    img3_path = os.path.join(test_dir, "gear_1.jpg")
    cv2.imwrite(img3_path, img3)
    test_images.append((img3_path, img3, (130, 130, 140, 140), {
        'name': '小齿轮',
        'type': '传动件',
        'specifications': '模数2，齿数24',
        'material': '合金钢',
        'supplier': 'DEF机械'
    }))
    
    print(f"✅ 创建了 {len(test_images)} 个测试图像")
    return test_images

def test_learning_process(learner, test_images):
    """测试学习过程"""
    print("\n📚 开始工件学习过程...")
    
    learned_workpieces = []
    
    for img_path, img, bbox, info in test_images:
        print(f"学习工件: {info['name']}")
        
        # 学习工件
        workpiece_id = learner.learn_workpiece(img, bbox, info)
        
        if workpiece_id:
            print(f"  ✅ 学习成功，ID: {workpiece_id}")
            learned_workpieces.append(workpiece_id)
            
            # 为每个工件添加额外样本（稍微变化的位置）
            for i in range(2):
                # 创建变化的边界框
                x, y, w, h = bbox
                new_x = max(0, x + np.random.randint(-10, 11))
                new_y = max(0, y + np.random.randint(-10, 11))
                new_bbox = (new_x, new_y, w, h)
                
                success = learner.add_workpiece_sample(workpiece_id, img, new_bbox)
                if success:
                    print(f"  ✅ 添加样本 {i+1} 成功")
                else:
                    print(f"  ❌ 添加样本 {i+1} 失败")
        else:
            print(f"  ❌ 学习失败")
    
    print(f"\n📊 学习统计:")
    workpiece_list = learner.get_workpiece_list()
    for workpiece in workpiece_list:
        print(f"  - {workpiece['name']}: {workpiece['learn_count']} 个样本")
    
    return learned_workpieces

def test_model_training(learner):
    """测试模型训练"""
    print("\n🤖 开始模型训练...")
    
    success = learner.train_model()
    
    if success:
        print("✅ 模型训练成功")
        
        # 获取模型信息
        model_info = learner.get_model_info()
        print(f"📊 模型信息:")
        print(f"  - 训练状态: {'已训练' if model_info['is_trained'] else '未训练'}")
        print(f"  - 工件数量: {model_info['workpiece_count']}")
        print(f"  - 特征类型: {model_info['feature_type']}")
        
        return True
    else:
        print("❌ 模型训练失败")
        return False

def test_recognition(learner, test_images):
    """测试识别功能"""
    print("\n🔍 开始识别测试...")
    
    correct_predictions = 0
    total_predictions = len(test_images)
    
    for img_path, img, bbox, expected_info in test_images:
        print(f"\n测试图像: {expected_info['name']}")
        
        # 执行识别
        result = learner.recognize_workpiece(img)
        
        if result['success']:
            predicted_name = result['workpiece_name']
            confidence = result['confidence']
            expected_name = expected_info['name']
            
            print(f"  预测结果: {predicted_name}")
            print(f"  期望结果: {expected_name}")
            print(f"  置信度: {confidence:.3f}")
            print(f"  位置: ({result['center_x']}, {result['center_y']})")
            
            if predicted_name == expected_name:
                print("  ✅ 识别正确")
                correct_predictions += 1
            else:
                print("  ❌ 识别错误")
                
            # 显示工件信息
            if result['workpiece_info']:
                info = result['workpiece_info']
                print(f"  工件信息:")
                print(f"    类型: {info.get('type', 'N/A')}")
                print(f"    规格: {info.get('specifications', 'N/A')}")
                print(f"    材料: {info.get('material', 'N/A')}")
        else:
            print(f"  ❌ 识别失败: {result.get('error', '未知错误')}")
    
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    print(f"\n📊 识别统计:")
    print(f"  - 总测试数: {total_predictions}")
    print(f"  - 正确识别: {correct_predictions}")
    print(f"  - 识别准确率: {accuracy:.1%}")
    
    return accuracy

def create_mixed_test_image(test_images):
    """创建包含多个工件的混合测试图像"""
    print("\n🖼️ 创建混合测试图像...")
    
    # 创建大图像
    mixed_img = np.zeros((600, 800, 3), dtype=np.uint8)
    mixed_img.fill(120)  # 灰色背景
    
    positions = [(150, 100), (450, 100), (300, 350)]
    
    for i, (img_path, img, bbox, info) in enumerate(test_images):
        if i >= len(positions):
            break
            
        # 提取工件区域
        x, y, w, h = bbox
        workpiece_roi = img[y:y+h, x:x+w]
        
        # 放置到混合图像中
        pos_x, pos_y = positions[i]
        mixed_img[pos_y:pos_y+h, pos_x:pos_x+w] = workpiece_roi
        
        print(f"  放置 {info['name']} 到位置 ({pos_x}, {pos_y})")
    
    # 保存混合图像
    mixed_path = "test_images/mixed_workpieces.jpg"
    cv2.imwrite(mixed_path, mixed_img)
    print(f"✅ 混合图像已保存: {mixed_path}")
    
    return mixed_path, mixed_img

def main():
    """主测试函数"""
    print("🚀 开始自定义工件学习系统测试")
    print("=" * 50)
    
    # 初始化学习器
    learner = CustomWorkpieceLearner("./test_workpiece_data")
    
    # 1. 创建测试图像
    test_images = create_test_images()
    
    # 2. 测试学习过程
    learned_workpieces = test_learning_process(learner, test_images)
    
    if not learned_workpieces:
        print("❌ 没有成功学习的工件，测试终止")
        return
    
    # 3. 测试模型训练
    training_success = test_model_training(learner)
    
    if not training_success:
        print("❌ 模型训练失败，跳过识别测试")
        return
    
    # 4. 测试识别功能
    accuracy = test_recognition(learner, test_images)
    
    # 5. 创建和测试混合图像
    mixed_path, mixed_img = create_mixed_test_image(test_images)
    
    print("\n🎯 测试混合图像识别...")
    mixed_result = learner.recognize_workpiece(mixed_img)
    if mixed_result['success']:
        print(f"✅ 混合图像识别成功: {mixed_result['workpiece_name']}")
    else:
        print(f"❌ 混合图像识别失败: {mixed_result.get('error', '未知错误')}")
    
    # 6. 测试总结
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"  - 学习工件数: {len(learned_workpieces)}")
    print(f"  - 模型训练: {'成功' if training_success else '失败'}")
    print(f"  - 识别准确率: {accuracy:.1%}")
    
    if accuracy >= 0.8:
        print("🎉 测试通过！系统运行良好")
    elif accuracy >= 0.5:
        print("⚠️ 测试部分通过，建议优化")
    else:
        print("❌ 测试失败，需要改进")

if __name__ == "__main__":
    main()
