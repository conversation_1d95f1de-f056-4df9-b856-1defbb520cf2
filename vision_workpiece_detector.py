#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DobotVisionStudio 工件视觉识别 Python 接口
基于DobotVisionStudio4.1.2的视觉识别系统Python封装

主要功能:
1. 图像预处理和增强
2. 圆形工件检测
3. 特征匹配检测
4. 坐标转换和机器人控制接口
5. TCP通信支持
"""

import cv2
import numpy as np
import socket
import threading
import queue
import time
import base64
import json
from datetime import datetime
from typing import Tuple, List, Dict, Optional
import logging

class VisionWorkpieceDetector:
    """基于OpenCV的工件视觉识别检测器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化视觉检测器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = self._setup_logger()
        self.config = self._load_config(config_file)
        
        # 检测参数 - 优化后的参数，减少误报
        self.circle_params = {
            'dp': 1,
            'min_dist': 80,     # 增加最小距离，避免重复检测
            'param1': 100,      # 提高Canny边缘检测的高阈值
            'param2': 50,       # 提高累加器阈值，减少误报
            'min_radius': 15,   # 提高最小半径
            'max_radius': 150   # 降低最大半径
        }
        
        self.template_params = {
            'match_threshold': 0.7,
            'scale_range': (0.8, 1.2),
            'angle_range': (-30, 30)
        }
        
        # 通信相关
        self.tcp_server = None
        self.vision_queue = queue.Queue()
        self.is_running = False
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('VisionDetector')
        logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def _load_config(self, config_file: str) -> dict:
        """加载配置文件"""
        default_config = {
            'tcp_port': 8888,
            'vision_trigger_port': 9999,
            'image_save_path': './captured_images/',
            'coordinate_scale': {'x': 1.0, 'y': 1.0},
            'roi': {'x': 0, 'y': 0, 'width': 640, 'height': 480}
        }
        
        if config_file:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            预处理后的图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯滤波去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 直方图均衡化增强对比度
        enhanced = cv2.equalizeHist(blurred)
        
        return enhanced
    
    def detect_circular_workpiece(self, image: np.ndarray) -> List[Dict]:
        """
        检测圆形工件
        
        Args:
            image: 输入图像
            
        Returns:
            检测结果列表，每个结果包含中心点坐标和半径
        """
        try:
            # 图像预处理
            processed = self.preprocess_image(image)
            
            # 使用HoughCircles检测圆形
            circles = cv2.HoughCircles(
                processed,
                cv2.HOUGH_GRADIENT,
                dp=self.circle_params['dp'],
                minDist=self.circle_params['min_dist'],
                param1=self.circle_params['param1'],
                param2=self.circle_params['param2'],
                minRadius=self.circle_params['min_radius'],
                maxRadius=self.circle_params['max_radius']
            )
            
            results = []
            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                for (x, y, r) in circles:
                    results.append({
                        'type': 'circle',
                        'center_x': float(x),
                        'center_y': float(y),
                        'radius': float(r),
                        'confidence': self._calculate_circle_confidence(processed, x, y, r),
                        'timestamp': datetime.now().isoformat()
                    })
            
            self.logger.info(f"检测到 {len(results)} 个圆形工件")
            return results
            
        except Exception as e:
            self.logger.error(f"圆形检测失败: {e}")
            return []
    
    def detect_template_workpiece(self, image: np.ndarray, template: np.ndarray) -> List[Dict]:
        """
        模板匹配检测工件
        
        Args:
            image: 输入图像
            template: 模板图像
            
        Returns:
            匹配结果列表
        """
        try:
            # 图像预处理
            img_gray = self.preprocess_image(image)
            template_gray = self.preprocess_image(template)
            
            results = []
            
            # 多尺度模板匹配
            for scale in np.arange(self.template_params['scale_range'][0], 
                                 self.template_params['scale_range'][1], 0.1):
                
                # 缩放模板
                scaled_template = cv2.resize(template_gray, None, fx=scale, fy=scale)
                
                if scaled_template.shape[0] > img_gray.shape[0] or scaled_template.shape[1] > img_gray.shape[1]:
                    continue
                
                # 模板匹配
                result = cv2.matchTemplate(img_gray, scaled_template, cv2.TM_CCOEFF_NORMED)
                locations = np.where(result >= self.template_params['match_threshold'])
                
                for pt in zip(*locations[::-1]):
                    h, w = scaled_template.shape
                    center_x = pt[0] + w // 2
                    center_y = pt[1] + h // 2
                    confidence = result[pt[1], pt[0]]
                    
                    results.append({
                        'type': 'template',
                        'center_x': float(center_x),
                        'center_y': float(center_y),
                        'width': float(w),
                        'height': float(h),
                        'scale': float(scale),
                        'confidence': float(confidence),
                        'timestamp': datetime.now().isoformat()
                    })
            
            # 非极大值抑制，去除重复检测
            results = self._non_max_suppression(results)
            
            self.logger.info(f"模板匹配检测到 {len(results)} 个工件")
            return results
            
        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return []
    
    def detect_edge_features(self, image: np.ndarray) -> List[Dict]:
        """
        边缘特征检测
        
        Args:
            image: 输入图像
            
        Returns:
            边缘特征检测结果
        """
        try:
            # 图像预处理
            processed = self.preprocess_image(image)
            
            # Canny边缘检测
            edges = cv2.Canny(processed, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            results = []
            for contour in contours:
                # 计算轮廓面积
                area = cv2.contourArea(contour)
                if area < 100:  # 过滤小轮廓
                    continue
                
                # 计算轮廓中心
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    center_x = int(M["m10"] / M["m00"])
                    center_y = int(M["m01"] / M["m00"])
                else:
                    continue
                
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                results.append({
                    'type': 'edge_feature',
                    'center_x': float(center_x),
                    'center_y': float(center_y),
                    'area': float(area),
                    'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                    'timestamp': datetime.now().isoformat()
                })
            
            self.logger.info(f"检测到 {len(results)} 个边缘特征")
            return results
            
        except Exception as e:
            self.logger.error(f"边缘特征检测失败: {e}")
            return []
    
    def _calculate_circle_confidence(self, image: np.ndarray, x: int, y: int, r: int) -> float:
        """计算圆形检测的置信度"""
        try:
            # 创建圆形掩码
            mask = np.zeros(image.shape, dtype=np.uint8)
            cv2.circle(mask, (x, y), r, 255, 2)
            
            # 计算边缘响应
            edges = cv2.Canny(image, 50, 150)
            edge_response = np.sum(cv2.bitwise_and(edges, mask))
            
            # 归一化置信度
            perimeter = 2 * np.pi * r
            confidence = edge_response / (perimeter * 255) if perimeter > 0 else 0
            
            return min(confidence, 1.0)
        except:
            return 0.5
    
    def _non_max_suppression(self, detections: List[Dict], overlap_threshold: float = 0.3) -> List[Dict]:
        """非极大值抑制，去除重复检测"""
        if not detections:
            return []
        
        # 按置信度排序
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        keep = []
        for i, det in enumerate(detections):
            should_keep = True
            for kept_det in keep:
                # 计算重叠度
                dist = np.sqrt((det['center_x'] - kept_det['center_x'])**2 + 
                              (det['center_y'] - kept_det['center_y'])**2)
                
                # 如果距离太近，认为是重复检测
                if dist < overlap_threshold * max(det.get('radius', 50), kept_det.get('radius', 50)):
                    should_keep = False
                    break
            
            if should_keep:
                keep.append(det)
        
        return keep
    
    def convert_to_robot_coordinates(self, detection_result: Dict) -> str:
        """
        将检测结果转换为机器人坐标
        
        Args:
            detection_result: 检测结果
            
        Returns:
            机器人坐标字符串
        """
        try:
            x = detection_result['center_x'] * self.config['coordinate_scale']['x']
            y = detection_result['center_y'] * self.config['coordinate_scale']['y']
            r = detection_result.get('angle', 0)
            
            return f"COORD:{x:.2f},{y:.2f},{r:.2f}"
        except Exception as e:
            self.logger.error(f"坐标转换失败: {e}")
            return "COORD:0,0,0"
    
    def encode_image_to_base64(self, image: np.ndarray, quality: int = 80) -> str:
        """
        将图像编码为Base64字符串
        
        Args:
            image: 输入图像
            quality: JPEG质量 (1-100)
            
        Returns:
            Base64编码的图像字符串
        """
        try:
            # 编码为JPEG
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, buffer = cv2.imencode('.jpg', image, encode_param)
            
            # 转换为Base64
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return f"IMAGE:{image_base64}"
        except Exception as e:
            self.logger.error(f"图像编码失败: {e}")
            return "IMAGE:"
    
    def start_tcp_server(self, port: int = None):
        """启动TCP服务器"""
        if port is None:
            port = self.config['tcp_port']
        
        self.is_running = True
        self.tcp_server = threading.Thread(target=self._tcp_server_worker, args=(port,))
        self.tcp_server.daemon = True
        self.tcp_server.start()
        
        self.logger.info(f"TCP服务器已启动，端口: {port}")
    
    def _tcp_server_worker(self, port: int):
        """TCP服务器工作线程"""
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            server_socket.bind(('0.0.0.0', port))
            server_socket.listen(5)
            
            while self.is_running:
                try:
                    client_socket, addr = server_socket.accept()
                    self.logger.info(f"客户端连接: {addr}")
                    
                    # 处理客户端请求
                    threading.Thread(target=self._handle_client, args=(client_socket,)).start()
                    
                except Exception as e:
                    if self.is_running:
                        self.logger.error(f"TCP服务器错误: {e}")
                        
        finally:
            server_socket.close()
    
    def _handle_client(self, client_socket: socket.socket):
        """处理客户端连接"""
        try:
            while self.is_running:
                data = client_socket.recv(1024).decode('utf-8')
                if not data:
                    break
                
                # 处理触发命令
                if data.strip() == "TRIGGER_VISION":
                    # 这里可以添加图像采集和处理逻辑
                    response = "VISION_TRIGGERED"
                    client_socket.send(response.encode('utf-8'))
                
        except Exception as e:
            self.logger.error(f"客户端处理错误: {e}")
        finally:
            client_socket.close()
    
    def stop_tcp_server(self):
        """停止TCP服务器"""
        self.is_running = False
        if self.tcp_server:
            self.tcp_server.join(timeout=2)
        self.logger.info("TCP服务器已停止")
    
    def save_detection_result(self, image: np.ndarray, results: List[Dict], filename: str = None):
        """
        保存检测结果图像
        
        Args:
            image: 原始图像
            results: 检测结果
            filename: 保存文件名
        """
        try:
            # 在图像上绘制检测结果
            result_image = image.copy()
            
            for result in results:
                x, y = int(result['center_x']), int(result['center_y'])
                
                if result['type'] == 'circle':
                    r = int(result['radius'])
                    cv2.circle(result_image, (x, y), r, (0, 255, 0), 2)
                    cv2.circle(result_image, (x, y), 2, (0, 0, 255), 3)
                    
                elif result['type'] == 'template':
                    w, h = int(result['width']), int(result['height'])
                    cv2.rectangle(result_image, (x-w//2, y-h//2), (x+w//2, y+h//2), (255, 0, 0), 2)
                
                # 添加文本标注
                confidence = result.get('confidence', 0)
                cv2.putText(result_image, f"{confidence:.2f}", (x-20, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 保存图像
            if filename is None:
                filename = f"detection_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            
            save_path = self.config['image_save_path'] + filename
            cv2.imwrite(save_path, result_image)
            
            self.logger.info(f"检测结果已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存检测结果失败: {e}")


# 使用示例
if __name__ == "__main__":
    # 创建检测器实例
    detector = VisionWorkpieceDetector()
    
    # 启动TCP服务器
    detector.start_tcp_server(8888)
    
    try:
        # 模拟图像处理
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 圆形检测
        circle_results = detector.detect_circular_workpiece(test_image)
        print("圆形检测结果:", circle_results)
        
        # 保持服务器运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("程序退出")
    finally:
        detector.stop_tcp_server()
