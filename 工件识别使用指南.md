# 🔧 自定义工件识别系统使用指南

## 📋 系统概述

您的视觉识别系统现在具备以下功能：

1. **ROI区域选择** - 手动框选检测区域，避免检测不需要的物体
2. **自定义工件学习** - 训练识别您自己的工件
3. **工件信息管理** - 存储和输出详细的工件信息
4. **多种检测算法** - 圆形检测、模板匹配、边缘特征、自定义学习

## 🚀 快速开始

### 第一步：训练您的工件

1. **启动工件学习界面**
   ```bash
   python workpiece_learning_gui.py
   ```

2. **加载工件图像**
   - 点击"加载单张图像"或"加载图像文件夹"
   - 选择包含您工件的图像

3. **标注工件区域**
   - 在图像上拖拽鼠标绘制红色矩形框
   - 框选您要学习的工件
   - 可以在同一图像中标注多个相同工件

4. **输入工件信息**
   ```
   工件名称: 例如 "M6螺丝"
   工件类型: 例如 "紧固件"
   规格: 例如 "M6x20mm"
   材料: 例如 "不锈钢"
   供应商: 例如 "ABC五金公司"
   备注: 例如 "十字槽头螺丝"
   ```

5. **学习工件**
   - 点击"学习当前标注"
   - 系统会保存工件图像和信息

6. **训练模型**
   - 学习多个工件后，点击"训练模型"
   - 等待训练完成

### 第二步：使用ROI功能识别工件

1. **启动主程序**
   ```bash
   python vision_demo.py
   ```

2. **加载图像**
   - 点击"加载图像"
   - 选择包含工件的图像

3. **启用ROI模式**
   - 勾选"启用区域选择"
   - 点击"绘制检测区域"

4. **绘制检测区域**
   - 在图像上拖拽鼠标绘制红色矩形
   - 框选您想要检测的区域

5. **选择检测算法**
   - 选择"自定义学习"算法

6. **开始检测**
   - 点击"开始检测"
   - 查看识别结果

## 🎯 识别结果说明

当系统成功识别工件时，会显示：

```
✅ 自定义识别成功: M6螺丝 (置信度: 0.892)
   类型: 紧固件
   规格: M6x20mm
   材料: 不锈钢
   供应商: ABC五金公司
```

## 💡 使用技巧

### 1. 提高识别准确率
- **多角度学习**: 为同一工件从不同角度拍摄多张图像
- **光照变化**: 在不同光照条件下学习工件
- **背景多样**: 在不同背景下学习工件
- **样本数量**: 每种工件至少学习5-10个样本

### 2. ROI使用建议
- **精确框选**: 尽量精确框选工件区域，避免包含过多背景
- **合适大小**: 确保ROI区域不要太小，给工件留一些边距
- **避免遮挡**: 确保工件在ROI区域内完整可见

### 3. 工件信息管理
- **命名规范**: 使用清晰、一致的工件命名
- **详细信息**: 填写完整的工件规格和供应商信息
- **定期更新**: 及时更新工件信息和添加新样本

## 🔧 故障排除

### 问题1: 检测到太多不需要的圆形
**解决方案**: 使用ROI功能
1. 勾选"启用区域选择"
2. 绘制检测区域框选目标工件
3. 只在框选区域内进行检测

### 问题2: 自定义工件识别失败
**解决方案**: 检查模型训练
1. 确保已学习足够的工件样本
2. 运行"训练模型"
3. 检查工件图像质量

### 问题3: 识别置信度低
**解决方案**: 改善训练数据
1. 增加更多工件样本
2. 确保样本图像清晰
3. 从多个角度学习工件

## 📁 文件说明

- `vision_demo.py` - 主程序，包含ROI功能
- `workpiece_learning_gui.py` - 工件学习界面
- `custom_workpiece_learner.py` - 工件学习核心模块
- `workpiece_data/` - 工件数据存储目录
  - `images/` - 工件图像
  - `models/` - 训练好的模型
  - `workpiece_database.json` - 工件信息数据库

## 🎉 完整工作流程示例

1. **准备工件图像** - 拍摄清晰的工件照片
2. **启动学习界面** - `python workpiece_learning_gui.py`
3. **标注和学习** - 框选工件，输入信息，学习样本
4. **训练模型** - 点击"训练模型"
5. **启动主程序** - `python vision_demo.py`
6. **ROI检测** - 框选区域，选择"自定义学习"，开始检测
7. **查看结果** - 获得工件名称和详细信息

现在您可以：
✅ 精确控制检测区域（ROI功能）
✅ 识别您训练的工件
✅ 获得完整的工件信息输出
✅ 避免检测不需要的物体
