# 项目文件说明

## 📁 核心文件

### 主程序文件
- **`vision_demo.py`** - 主界面程序，集成所有功能
- **`workpiece_learning_gui.py`** - 专用学习标注界面
- **`custom_workpiece_learner.py`** - 自定义学习核心模块

### 传统检测模块
- **`vision_workpiece_detector.py`** - 传统检测算法（圆形、模板匹配等）
- **`plus.py`** - 硬件连接和通信模块

### 配置和测试
- **`vision_config.json`** - 系统配置文件
- **`test_custom_learning.py`** - 系统功能测试脚本

### 文档
- **`README.md`** - 项目说明文档
- **`自定义工件学习识别系统使用说明.md`** - 详细使用指南

## 📂 数据目录

### `workpiece_data/`
- **`images/`** - 存储学习的工件图像
- **`models/`** - 存储训练好的机器学习模型
- **`annotations/`** - 存储标注数据（预留）

## 🚀 启动方式

### 主要使用方式
```bash
python vision_demo.py
```

### 独立学习界面
```bash
python workpiece_learning_gui.py
```

### 系统测试
```bash
python test_custom_learning.py
```

## 🔧 文件依赖关系

```
vision_demo.py
├── custom_workpiece_learner.py
├── vision_workpiece_detector.py
├── workpiece_learning_gui.py (通过按钮调用)
└── vision_config.json

workpiece_learning_gui.py
└── custom_workpiece_learner.py

custom_workpiece_learner.py
├── workpiece_data/ (数据存储)
└── sklearn, cv2, numpy (外部依赖)
```

## 📋 功能模块

### 1. 主界面 (`vision_demo.py`)
- 图像加载和显示
- 多种检测算法选择
- 硬件连接和通信
- 结果可视化

### 2. 学习界面 (`workpiece_learning_gui.py`)
- 图像标注
- 工件信息输入
- 样本管理
- 模型训练

### 3. 学习引擎 (`custom_workpiece_learner.py`)
- 特征提取
- 机器学习训练
- 工件识别
- 数据管理

### 4. 传统检测 (`vision_workpiece_detector.py`)
- 圆形检测
- 模板匹配
- 边缘检测
- TCP通信

### 5. 硬件接口 (`plus.py`)
- 视觉硬件连接
- 数据接收和发送
- 图像触发

## 🗂️ 配置文件

### `vision_config.json`
包含系统的所有配置参数：
- 通信设置
- 检测算法参数
- 坐标系配置
- 硬件连接设置

## 🧪 测试文件

### `test_custom_learning.py`
完整的系统测试，包括：
- 创建测试工件图像
- 模拟学习过程
- 模型训练验证
- 识别精度测试

## 📊 数据流程

1. **学习阶段**：
   - 图像 → 标注 → 特征提取 → 数据库存储

2. **训练阶段**：
   - 数据库 → 特征准备 → 模型训练 → 模型保存

3. **识别阶段**：
   - 图像 → 特征提取 → 模型预测 → 结果输出

## 🔄 版本控制

项目已清理无关文件，保留核心功能：
- ✅ 删除了测试和调试文件
- ✅ 删除了分析文档
- ✅ 删除了C#相关文件
- ✅ 删除了缓存文件
- ✅ 保留了核心Python模块
- ✅ 保留了配置和文档

## 💡 使用建议

1. **首次使用**：先运行 `test_custom_learning.py` 验证系统功能
2. **学习工件**：使用 `workpiece_learning_gui.py` 进行标注学习
3. **日常使用**：使用 `vision_demo.py` 进行识别检测
4. **硬件集成**：配置 `vision_config.json` 中的硬件参数

## 📞 技术支持

如需帮助，请参考：
- `README.md` - 快速开始指南
- `自定义工件学习识别系统使用说明.md` - 详细使用说明
- 运行测试脚本验证系统状态
