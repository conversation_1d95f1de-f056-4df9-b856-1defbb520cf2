#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滚动功能的简化版本
"""

import tkinter as tk
from tkinter import ttk

class TestScrollGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("测试滚动功能")
        self.root.geometry("800x600")
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板容器
        control_container = ttk.LabelFrame(main_frame, text="控制面板", width=300)
        control_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_container.pack_propagate(False)
        
        # 创建可滚动的控制面板
        self.setup_scrollable_control_panel(control_container)
        
        # 右侧显示区域
        display_frame = ttk.LabelFrame(main_frame, text="显示区域")
        display_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加一些测试内容
        test_label = ttk.Label(display_frame, text="这是显示区域\n滚动功能测试", 
                              font=("Arial", 16), justify=tk.CENTER)
        test_label.pack(expand=True)
        
    def setup_scrollable_control_panel(self, parent):
        """设置可滚动的控制面板"""
        # 创建Canvas和Scrollbar
        self.control_canvas = tk.Canvas(parent, highlightthickness=0)
        self.control_scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.control_canvas.yview)
        self.control_canvas.configure(yscrollcommand=self.control_scrollbar.set)
        
        # 创建可滚动的框架
        self.scrollable_frame = ttk.Frame(self.control_canvas)
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
        )
        
        # 将框架添加到Canvas
        self.canvas_frame = self.control_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # 布局Canvas和Scrollbar
        self.control_canvas.pack(side="left", fill="both", expand=True)
        self.control_scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        self.bind_mousewheel_to_canvas()
        
        # 绑定Canvas大小变化事件，确保内容框架宽度适应
        self.control_canvas.bind('<Configure>', self.on_canvas_configure)
        
        # 设置控制面板内容
        self.setup_control_panel_content(self.scrollable_frame)
        
    def bind_mousewheel_to_canvas(self):
        """绑定鼠标滚轮事件到Canvas"""
        def _on_mousewheel(event):
            # 检查鼠标是否在控制面板区域内
            if self.control_canvas.winfo_containing(event.x_root, event.y_root) == self.control_canvas:
                self.control_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        def _bind_to_mousewheel(event):
            self.control_canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        def _unbind_from_mousewheel(event):
            self.control_canvas.unbind_all("<MouseWheel>")
        
        # 绑定鼠标进入和离开事件
        self.control_canvas.bind('<Enter>', _bind_to_mousewheel)
        self.control_canvas.bind('<Leave>', _unbind_from_mousewheel)
        
    def on_canvas_configure(self, event):
        """Canvas大小变化时调整内容框架宽度"""
        canvas_width = event.width
        self.control_canvas.itemconfig(self.canvas_frame, width=canvas_width)
        
    def setup_control_panel_content(self, parent):
        """设置控制面板内容 - 创建很多控件来测试滚动"""
        # 文件操作
        file_frame = ttk.LabelFrame(parent, text="文件操作")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(file_frame, text="加载图像").pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="加载模板").pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="保存结果").pack(fill=tk.X, pady=2)
        
        # 检测算法选择
        algo_frame = ttk.LabelFrame(parent, text="检测算法")
        algo_frame.pack(fill=tk.X, pady=5)
        
        algo_var = tk.StringVar(value="custom")
        ttk.Radiobutton(algo_frame, text="圆形检测", variable=algo_var, value="circle").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="模板匹配", variable=algo_var, value="template").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="边缘特征", variable=algo_var, value="edge").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="自定义工件识别", variable=algo_var, value="custom").pack(anchor=tk.W)
        
        # 参数设置
        param_frame = ttk.LabelFrame(parent, text="参数设置")
        param_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(param_frame, text="最小半径:").pack(anchor=tk.W)
        ttk.Scale(param_frame, from_=5, to=50, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="最大半径:").pack(anchor=tk.W)
        ttk.Scale(param_frame, from_=50, to=500, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="匹配阈值:").pack(anchor=tk.W)
        ttk.Scale(param_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # ROI选择控制
        roi_frame = ttk.LabelFrame(parent, text="检测区域选择")
        roi_frame.pack(fill=tk.X, pady=5)
        
        ttk.Checkbutton(roi_frame, text="启用区域选择").pack(anchor=tk.W, pady=2)
        ttk.Button(roi_frame, text="绘制检测区域").pack(fill=tk.X, pady=1)
        ttk.Button(roi_frame, text="锁定区域").pack(fill=tk.X, pady=1)
        ttk.Button(roi_frame, text="清除区域").pack(fill=tk.X, pady=1)
        
        # 执行按钮
        action_frame = ttk.LabelFrame(parent, text="执行操作")
        action_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(action_frame, text="开始检测").pack(fill=tk.X, pady=2)
        ttk.Button(action_frame, text="清除结果").pack(fill=tk.X, pady=2)
        
        # 测试功能
        test_frame = ttk.LabelFrame(parent, text="测试功能")
        test_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(test_frame, text="测试图像显示").pack(fill=tk.X, pady=1)
        ttk.Button(test_frame, text="创建测试图像").pack(fill=tk.X, pady=1)
        
        # 自定义学习功能
        custom_frame = ttk.LabelFrame(parent, text="自定义工件识别")
        custom_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(custom_frame, text="识别您训练的工件:", font=("Arial", 9, "bold")).pack(anchor=tk.W, pady=2)
        ttk.Button(custom_frame, text="打开学习界面").pack(fill=tk.X, pady=1)
        ttk.Button(custom_frame, text="训练模型").pack(fill=tk.X, pady=1)
        
        # 硬件连接控制
        hardware_frame = ttk.LabelFrame(parent, text="视觉硬件连接")
        hardware_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(hardware_frame, text="等待连接").pack()
        ttk.Checkbutton(hardware_frame, text="自动检测硬件图像").pack(anchor=tk.W, pady=2)
        ttk.Button(hardware_frame, text="触发拍照").pack(fill=tk.X, pady=2)
        ttk.Button(hardware_frame, text="重连硬件").pack(fill=tk.X, pady=2)
        
        # TCP服务器控制
        tcp_frame = ttk.LabelFrame(parent, text="TCP服务器")
        tcp_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(tcp_frame, text="未启动").pack()
        ttk.Button(tcp_frame, text="启动服务器").pack(fill=tk.X, pady=2)
        ttk.Button(tcp_frame, text="停止服务器").pack(fill=tk.X, pady=2)
        
        # 结果显示
        result_frame = ttk.LabelFrame(parent, text="检测结果")
        result_frame.pack(fill=tk.X, pady=5)
        
        result_text = tk.Text(result_frame, height=8, width=30)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_text.yview)
        result_text.configure(yscrollcommand=scrollbar.set)
        
        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加更多测试内容
        for i in range(5):
            extra_frame = ttk.LabelFrame(parent, text=f"额外功能 {i+1}")
            extra_frame.pack(fill=tk.X, pady=5)
            
            for j in range(3):
                ttk.Button(extra_frame, text=f"按钮 {j+1}").pack(fill=tk.X, pady=1)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = TestScrollGUI()
    app.run()
