#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证自定义工件识别功能
"""

import tkinter as tk
from tkinter import messagebox
import cv2
import numpy as np
from custom_workpiece_learner import CustomWorkpieceLearner

def main():
    """主验证函数"""
    print("🔍 验证自定义工件识别功能...")
    
    # 创建学习器
    learner = CustomWorkpieceLearner()
    
    # 检查模型
    model_info = learner.get_model_info()
    print(f"📊 模型状态: 已训练={model_info['is_trained']}, 工件数量={model_info['workpiece_count']}")
    
    if not learner.is_trained:
        learner.load_model()
    
    # 获取工件列表
    workpiece_list = learner.get_workpiece_list()
    print(f"\n📋 您已训练的工件:")
    for i, w in enumerate(workpiece_list, 1):
        print(f"   {i}. {w['name']} ({w['type']})")
    
    print(f"\n✅ 系统状态正常！")
    print(f"\n🎯 在 vision_demo.py 中的正确操作:")
    print(f"   1. 加载包含以上工件的图像")
    print(f"   2. 选择 '🎯 自定义工件识别' 算法 (不要选圆形检测!)")
    print(f"   3. 勾选 '启用区域选择'")
    print(f"   4. 在图像上框选工件区域")
    print(f"   5. 点击 '开始检测'")
    print(f"\n💡 系统会输出:")
    print(f"   - 工件名称 (如: wife模块, 绿色小铁片, 电子罗盘)")
    print(f"   - 置信度")
    print(f"   - 详细信息 (类型、材料等)")
    
    # 创建GUI提示
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    message = f"""✅ 自定义工件识别系统验证成功！

📋 您已训练的工件 ({len(workpiece_list)} 个):
""" + "\n".join([f"• {w['name']} ({w['type']})" for w in workpiece_list[:5]]) + f"""

🎯 正确使用方法:
1. 在 vision_demo.py 中选择 "🎯 自定义工件识别"
2. 不要选择 "圆形检测"！
3. 使用ROI框选工件区域
4. 开始检测获得工件信息

💡 如果还是显示圆形，请检查是否选择了正确的算法！"""
    
    messagebox.showinfo("验证结果", message)
    root.destroy()

if __name__ == "__main__":
    main()
