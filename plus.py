# =================================================================================
#  机器人视觉控制系统 - V14.0 (集成滑轨控制版)
# =================================================================================
import customtkinter as ctk
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os
import ctypes
import struct

# --- 1. 全局配置 ---
ROBOT_IP = "***********"
PYTHON_PC_IP = "*************"
DASHBOARD_PORT = 29999
MOTION_PORT = 30003
VISION_SERVER_PORT = 6005
GRIPPER_IO_INDEX = 1
VISION_TRIGGER_PORT = 6006
VISION_TRIGGER_CMD = "TRIGGER"

# --- 滑轨控制配置 ---
SLIDE_RAIL_ID = 9  # Modbus设备ID
SLIDE_RAIL_BAUDRATE = 115200
SLIDE_RAIL_CONTROL_REG = 1000  # 控制寄存器地址
SLIDE_RAIL_STATUS_REG = 2000   # 状态寄存器地址
SLIDE_RAIL_POSITION_REG = 2001 # 位置寄存器地址
SLIDE_RAIL_RANGE_MM = 1000     # 滑轨行程范围(mm) - 可后续调整

def send_cmd(sock, cmd, log_prefix="CMD"):
    try:
        full_cmd = cmd + "\n"
        print(f"[{log_prefix}] SND: {full_cmd.strip()}")
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"[{log_prefix}] RCV: {response}")
        parts = response.split(',')
        error_id_str = parts[0]
        if error_id_str == '0': return True
        else:
            print(f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}")
            return False
    except socket.error as e:
        print(f"❌ 发送指令 '{cmd}' 时网络错误: {e}")
        return False

# =================================================================================
# 滑轨控制类
# =================================================================================
class SlideRailController:
    """滑轨控制器 - 基于Modbus协议"""

    def __init__(self, robot_socket=None):
        self.robot_socket = robot_socket
        self.current_position_percent = 0.0  # 当前位置百分比 (0-100)
        self.current_position_mm = 0.0       # 当前位置毫米数
        self.is_connected = False
        self.range_mm = SLIDE_RAIL_RANGE_MM  # 滑轨行程范围

    def connect(self):
        """连接滑轨控制器"""
        try:
            # 这里使用机器人的socket连接来发送Modbus指令
            if self.robot_socket:
                self.is_connected = True
                return True, "滑轨连接成功"
            else:
                return False, "机器人未连接，无法连接滑轨"
        except Exception as e:
            return False, f"滑轨连接失败: {str(e)}"

    def disconnect(self):
        """断开滑轨连接"""
        self.is_connected = False

    def percent_to_register_value(self, percent):
        """将百分比位置转换为寄存器值"""
        # 根据JODELL滑轨的换算公式
        return int((100 - percent) * 255 / 100.0)

    def register_value_to_percent(self, reg_value):
        """将寄存器值转换为百分比位置"""
        # 根据JODELL滑轨的换算公式
        return int((100 - reg_value * 100 / 255.0) + 0.5)

    def percent_to_mm(self, percent):
        """将百分比位置转换为毫米位置"""
        return (percent / 100.0) * self.range_mm

    def mm_to_percent(self, mm):
        """将毫米位置转换为百分比位置"""
        return (mm / self.range_mm) * 100.0

    def move_to_position(self, position_percent, speed=50, force=50):
        """移动滑轨到指定位置百分比"""
        if not self.is_connected:
            return False, "滑轨未连接"

        try:
            # 限制位置范围
            position_percent = max(0, min(100, position_percent))

            # 转换为寄存器值
            reg_value = self.percent_to_register_value(position_percent)

            # 构造Modbus写寄存器指令
            modbus_cmd = f"ModbusRTUCreate({SLIDE_RAIL_ID},{SLIDE_RAIL_BAUDRATE},0,1,8,1,1)"
            success = send_cmd(self.robot_socket, modbus_cmd, "SLIDE")

            if success:
                # 发送位置控制指令
                write_cmd = f"ModbusWrite({SLIDE_RAIL_ID},{SLIDE_RAIL_CONTROL_REG},1,{reg_value})"
                success = send_cmd(self.robot_socket, write_cmd, "SLIDE")

                if success:
                    self.current_position_percent = position_percent
                    self.current_position_mm = self.percent_to_mm(position_percent)
                    return True, f"滑轨移动到位置 {position_percent:.1f}% ({self.current_position_mm:.1f}mm)"
                else:
                    return False, f"滑轨移动失败"
            else:
                return False, f"Modbus连接失败"

        except Exception as e:
            return False, f"滑轨控制异常: {str(e)}"

    def get_current_position(self):
        """获取滑轨当前位置"""
        if not self.is_connected:
            return False, 0, "滑轨未连接"

        try:
            # 读取位置寄存器
            read_cmd = f"ModbusRead({SLIDE_RAIL_ID},{SLIDE_RAIL_POSITION_REG},1)"
            success = send_cmd(self.robot_socket, read_cmd, "SLIDE")

            if success:
                # 这里需要根据实际响应格式来解析位置值
                # 暂时返回当前记录的位置
                return True, self.current_position_percent, f"当前位置: {self.current_position_percent:.1f}% ({self.current_position_mm:.1f}mm)"
            else:
                return False, 0, f"位置读取失败"

        except Exception as e:
            return False, 0, f"位置读取异常: {str(e)}"

    def calculate_coordinate_offset(self, rail_position_percent):
        """根据滑轨位置计算机械臂坐标偏移量"""
        # 滑轨沿X轴移动，计算X轴偏移量
        rail_position_mm = self.percent_to_mm(rail_position_percent)

        # 假设滑轨0%位置对应机械臂基础坐标系的X=0
        # 滑轨移动会改变机械臂的实际X坐标
        offset_x = rail_position_mm
        offset_y = 0  # Y轴不受滑轨影响
        offset_z = 0  # Z轴不受滑轨影响

        return offset_x, offset_y, offset_z

class RobotControlApp(ctk.CTk):
    def __init__(self):
        # ... (无变化)
        super().__init__()
        self.title("机器人视觉控制系统 (MG400) - 集成滑轨控制版")
        self.geometry("900x700")
        self.dashboard_socket = None
        self.motion_socket = None
        self.vision_queue = queue.Queue()
        self.is_robot_connected = False

        # 初始化滑轨控制器
        self.slide_rail = SlideRailController()

        self.create_widgets()
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()
        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        if is_admin: self.log("✅ 程序正以【管理员权限】运行。", "lightgreen")
        else: self.log("⚠️ 程序正以【普通用户权限】运行。", "orange")

    def create_widgets(self):
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=10, pady=10, fill="both", expand=True)
        
        # ▼▼▼▼▼ 【核心修复】修正此处的拼写错误 ▼▼▼▼▼
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
        
        self.left_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        # ... (后续UI代码无变化)
        manual_control_frame = ctk.CTkFrame(self.left_frame)
        manual_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(manual_control_frame, text="机器人人机控制", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=2, pady=10)
        btn_x_plus = ctk.CTkButton(manual_control_frame, text="X+"); btn_x_minus = ctk.CTkButton(manual_control_frame, text="X-"); btn_y_plus = ctk.CTkButton(manual_control_frame, text="Y+"); btn_y_minus = ctk.CTkButton(manual_control_frame, text="Y-"); btn_z_plus = ctk.CTkButton(manual_control_frame, text="Z+"); btn_z_minus = ctk.CTkButton(manual_control_frame, text="Z-")
        btn_x_plus.grid(row=1, column=0, padx=5, pady=5, sticky="ew"); btn_x_minus.grid(row=1, column=1, padx=5, pady=5, sticky="ew"); btn_y_plus.grid(row=2, column=0, padx=5, pady=5, sticky="ew"); btn_y_minus.grid(row=2, column=1, padx=5, pady=5, sticky="ew"); btn_z_plus.grid(row=3, column=0, padx=5, pady=5, sticky="ew"); btn_z_minus.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        btn_x_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("X+")); btn_x_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("X-")); btn_y_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y+")); btn_y_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y-")); btn_z_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z+")); btn_z_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z-"))
        for btn in [btn_x_plus, btn_x_minus, btn_y_plus, btn_y_minus, btn_z_plus, btn_z_minus]: btn.bind("<ButtonRelease-1>", self.stop_jog)
        self.btn_home = ctk.CTkButton(manual_control_frame, text="回原点", command=self.go_home); self.btn_home.grid(row=4, column=0, columnspan=2, pady=10, padx=5, sticky="ew")
        vision_control_frame = ctk.CTkFrame(self.left_frame); vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="视觉控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkButton(vision_control_frame, text="拍照", command=self.trigger_vision_capture).pack(pady=5, padx=5, fill="x")
        connect_frame = ctk.CTkFrame(self.left_frame); connect_frame.pack(pady=20, padx=10, fill="x", side="bottom")

        # 状态显示区域
        status_frame = ctk.CTkFrame(connect_frame); status_frame.pack(fill="x", pady=5)
        self.connect_label = ctk.CTkLabel(status_frame, text="机器人未连接", text_color="orange"); self.connect_label.pack(side="left", padx=10)
        self.slide_rail_label = ctk.CTkLabel(status_frame, text="滑轨未连接", text_color="orange"); self.slide_rail_label.pack(side="left", padx=10)

        # 控制按钮区域
        control_frame = ctk.CTkFrame(connect_frame); control_frame.pack(fill="x", pady=5)
        self.btn_connect = ctk.CTkButton(control_frame, text="连接机器人", command=self.handle_connect_button_click); self.btn_connect.pack(side="right", padx=10)
        self.auto_run_switch = ctk.CTkSwitch(control_frame, text="自动抓取", onvalue=True, offvalue=False); self.auto_run_switch.pack(side="right", padx=10)
        self.right_frame = ctk.CTkFrame(self.main_frame); self.right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        ctk.CTkLabel(self.right_frame, text="监控界面", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        self.image_display_label = ctk.CTkLabel(self.right_frame, text="[等待视觉软件发送图像...]", bg_color="grey30", height=300); self.image_display_label.pack(pady=10, padx=10, fill="both", expand=True)
        ctk.CTkLabel(self.right_frame, text="信息显示/日志", font=ctk.CTkFont(size=14)).pack()
        self.log_textbox = ctk.CTkTextbox(self.right_frame, state="disabled", height=200); self.log_textbox.pack(pady=10, padx=10, fill="both", expand=True)

    # (后续所有函数都无变化，为简洁省略)
    # ...
    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal"); self.log_textbox.tag_config(f"tag_{color}", foreground=color); self.log_textbox.insert("end", f"{message}\n", f"tag_{color}"); self.log_textbox.configure(state="disabled"); self.log_textbox.see("end")
    def handle_connect_button_click(self):
        if not self.is_robot_connected:
            try:
                self.log(f"正在连接机器人 at {ROBOT_IP}...", "cyan"); self.dashboard_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=5); self.motion_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=5)
                self.log("正在尝试使能机器人...", "yellow")
                if not send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"): raise ConnectionError("机器人使能失败")
                self.log("机器人已使能，等待伺服系统稳定...", "yellow"); time.sleep(1)

                # 初始化滑轨连接
                self.slide_rail.robot_socket = self.dashboard_socket
                success, message = self.slide_rail.connect()
                if success:
                    self.log(f"✅ {message}", "green")
                    self.slide_rail_label.configure(text="滑轨已连接", text_color="green")
                else:
                    self.log(f"⚠️ {message}", "orange")
                    self.slide_rail_label.configure(text="滑轨连接失败", text_color="red")

                self.is_robot_connected = True; self.log("✅ 机器人连接并使能成功!", "green"); self.connect_label.configure(text="机器人已连接", text_color="green"); self.btn_connect.configure(text="断开连接")
            except Exception as e:
                self.log(f"❌ 连接失败: {e}", "red")
                if self.dashboard_socket: self.dashboard_socket.close()
                if self.motion_socket: self.motion_socket.close()
        else:
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH");

            # 断开滑轨连接
            self.slide_rail.disconnect()
            self.log("🔌 滑轨已断开", "orange")
            self.slide_rail_label.configure(text="滑轨未连接", text_color="orange")

            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close();
            self.is_robot_connected = False; self.log("🔌 机器人已断开。", "orange"); self.connect_label.configure(text="机器人未连接", text_color="orange"); self.btn_connect.configure(text="连接机器人")
    def trigger_vision_capture(self):
        self.log("📸 发送拍照触发指令...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"; s.sendall(cmd_to_send.encode('utf-8')); self.log("✅ 触发指令已发送成功。", "green")
        except socket.timeout: self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e: self.log(f"❌ 触发失败: {e}", "red")
    def start_jog(self, axis_id):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log(f"🤖 开始点动: {axis_id}", "cyan"); send_cmd(self.motion_socket, f"MoveJog({axis_id})", "MOT")
    def stop_jog(self, event=None):
        if not self.is_robot_connected: return
        self.log("🤖 停止点动", "cyan"); send_cmd(self.motion_socket, "MoveJog()", "MOT"); time.sleep(0.2)
    def go_home(self):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log("🤖 正在移动到安全原点...");
        if send_cmd(self.motion_socket, "MoveJ(200, 0, 50, 0)", "MOT"):
            send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("✅ 已到达原点。")
        else: self.log("❌ 回原点失败。", "red")
    def execute_pick_and_place(self, target_x, target_y, target_r):
        if not self.is_robot_connected: self.log("⚠️ 自动抓取失败：机器人未连接", "orange"); return

        # 获取滑轨当前位置并计算坐标偏移
        if self.slide_rail.is_connected:
            success, rail_position, position_msg = self.slide_rail.get_current_position()
            if success:
                self.log(f"📏 滑轨{position_msg}", "cyan")
                # 计算坐标偏移量
                offset_x, offset_y, offset_z = self.slide_rail.calculate_coordinate_offset(rail_position)

                # 应用坐标变换
                adjusted_target_x = target_x + offset_x
                adjusted_target_y = target_y + offset_y

                self.log(f"🔄 坐标变换: 原始({target_x:.1f}, {target_y:.1f}) -> 调整后({adjusted_target_x:.1f}, {adjusted_target_y:.1f})", "yellow")

                # 使用调整后的坐标
                target_x = adjusted_target_x
                target_y = adjusted_target_y
            else:
                self.log(f"⚠️ 滑轨位置读取失败，使用原始坐标: {position_msg}", "orange")
        else:
            self.log("⚠️ 滑轨未连接，使用原始坐标", "orange")

        self.log(f"🤖 开始执行抓取任务..."); pickup_z_high, pickup_z_low = 50, 10; place_x, place_y, place_z = 150, -150, 50
        try:
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.motion_socket, f"MoveJ({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_low}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("🤏 抓取: 闭合夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveJ({place_x}, {place_y}, {place_z}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("👐 放置: 张开夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            self.go_home(); self.log("✅ 抓取任务完成!", "green")
        except Exception as e: self.log(f"❌ 机器人执行动作时出错: {e}", "red")
    def process_vision_queue(self):
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包: {message}", "cyan")
                parts = message.split(';')
                image_path = parts[-1].strip()
                self.show_image_from_path(image_path)
                if len(parts) >= 2 and self.auto_run_switch.get() and self.is_robot_connected:
                    coord_data = parts[0]
                    try:
                        coord_parts = coord_data.split(',');
                        if len(coord_parts) >= 2:
                            robot_x, robot_y = float(coord_parts[0]), float(coord_parts[1]); robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                            self.execute_pick_and_place(robot_x, robot_y, robot_r)
                        else: self.log(f"⚠️ 坐标部分格式无法解析: {coord_data}", "orange")
                    except (ValueError, IndexError) as e: self.log(f"❌ 解析坐标数据失败: {e}", "red")
        except queue.Empty: pass
        self.after(100, self.process_vision_queue)
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")
    def vision_listener_thread(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT)); s.listen()
                print(f"👂 视觉服务器已启动，正在监听端口 {VISION_SERVER_PORT}...")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        print(f"🤝 视觉软件已连接: {addr}")
                        while True:
                            data = conn.recv(1024)
                            if not data: print("🔌 视觉软件已断开。"); break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e: print(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")
    def on_closing(self):
        if self.is_robot_connected:
            self.log("正在断开机器人连接..."); send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            # 断开滑轨连接
            self.slide_rail.disconnect()
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close()
        self.destroy()

    # =================================================================================
    # 滑轨控制辅助方法
    # =================================================================================

    def move_slide_rail_to_position(self, position_percent):
        """手动移动滑轨到指定位置"""
        if not self.slide_rail.is_connected:
            self.log("⚠️ 滑轨未连接", "orange")
            return False

        success, message = self.slide_rail.move_to_position(position_percent)
        if success:
            self.log(f"✅ {message}", "green")
        else:
            self.log(f"❌ 滑轨移动失败: {message}", "red")
        return success

    def get_slide_rail_status(self):
        """获取滑轨状态信息"""
        if not self.slide_rail.is_connected:
            return "滑轨未连接"

        success, position, message = self.slide_rail.get_current_position()
        if success:
            return f"滑轨位置: {position:.1f}% ({self.slide_rail.current_position_mm:.1f}mm)"
        else:
            return f"滑轨状态读取失败: {message}"

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = RobotControlApp()
    app.mainloop()