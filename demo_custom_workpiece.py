#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义工件识别演示脚本
展示如何训练和识别自定义工件
"""

import cv2
import numpy as np
import os
from custom_workpiece_learner import CustomWorkpieceLearner

def create_sample_workpieces():
    """创建示例工件图像用于演示"""
    print("🔧 创建示例工件图像...")
    
    # 创建一个包含不同工件的图像
    img = np.zeros((600, 800, 3), dtype=np.uint8)
    img.fill(80)  # 深灰色背景
    
    # 工件1: 螺丝 (矩形)
    cv2.rectangle(img, (100, 100), (180, 200), (200, 200, 200), -1)
    cv2.rectangle(img, (120, 80), (160, 100), (150, 150, 150), -1)  # 螺丝头
    cv2.putText(img, "Screw", (100, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 工件2: 螺母 (六边形)
    center = (300, 150)
    radius = 40
    points = []
    for i in range(6):
        angle = i * 60 * np.pi / 180
        x = int(center[0] + radius * np.cos(angle))
        y = int(center[1] + radius * np.sin(angle))
        points.append([x, y])
    points = np.array(points, np.int32)
    cv2.fillPoly(img, [points], (180, 180, 180))
    cv2.putText(img, "Nut", (270, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 工件3: 垫片 (圆环)
    cv2.circle(img, (500, 150), 50, (160, 160, 160), -1)
    cv2.circle(img, (500, 150), 25, (80, 80, 80), -1)  # 中心孔
    cv2.putText(img, "Washer", (460, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 工件4: 齿轮 (复杂形状)
    center = (650, 150)
    # 绘制齿轮主体
    cv2.circle(img, center, 45, (170, 170, 170), -1)
    # 绘制齿
    for i in range(8):
        angle = i * 45 * np.pi / 180
        x1 = int(center[0] + 35 * np.cos(angle))
        y1 = int(center[1] + 35 * np.sin(angle))
        x2 = int(center[0] + 55 * np.cos(angle))
        y2 = int(center[1] + 55 * np.sin(angle))
        cv2.line(img, (x1, y1), (x2, y2), (170, 170, 170), 8)
    cv2.circle(img, center, 15, (80, 80, 80), -1)  # 中心孔
    cv2.putText(img, "Gear", (620, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 在下半部分重复一些工件（用于测试识别）
    # 螺丝
    cv2.rectangle(img, (150, 350), (230, 450), (200, 200, 200), -1)
    cv2.rectangle(img, (170, 330), (210, 350), (150, 150, 150), -1)
    
    # 螺母
    center2 = (400, 400)
    points2 = []
    for i in range(6):
        angle = i * 60 * np.pi / 180
        x = int(center2[0] + radius * np.cos(angle))
        y = int(center2[1] + radius * np.sin(angle))
        points2.append([x, y])
    points2 = np.array(points2, np.int32)
    cv2.fillPoly(img, [points2], (180, 180, 180))
    
    # 添加一些噪声
    noise = np.random.randint(0, 15, img.shape, dtype=np.uint8)
    img = cv2.add(img, noise)
    
    # 保存图像
    cv2.imwrite("sample_workpieces.jpg", img)
    print("✅ 示例工件图像已保存: sample_workpieces.jpg")
    
    return img

def demo_workpiece_learning():
    """演示工件学习过程"""
    print("\n🎓 开始工件学习演示...")
    
    # 创建学习器
    learner = CustomWorkpieceLearner()
    
    # 创建示例图像
    img = create_sample_workpieces()
    
    # 定义工件信息和边界框
    workpieces = [
        {
            'name': 'M6螺丝',
            'bbox': (100, 100, 80, 100),  # x, y, width, height
            'info': {
                'name': 'M6螺丝',
                'type': '紧固件',
                'specifications': 'M6x20mm',
                'material': '不锈钢',
                'supplier': 'ABC五金公司',
                'notes': '十字槽头螺丝'
            }
        },
        {
            'name': '六角螺母',
            'bbox': (260, 110, 80, 80),
            'info': {
                'name': '六角螺母',
                'type': '紧固件',
                'specifications': 'M6',
                'material': '碳钢',
                'supplier': 'XYZ机械厂',
                'notes': '标准六角螺母'
            }
        },
        {
            'name': '平垫片',
            'bbox': (450, 100, 100, 100),
            'info': {
                'name': '平垫片',
                'type': '密封件',
                'specifications': '内径6mm，外径12mm',
                'material': '不锈钢',
                'supplier': 'DEF密封公司',
                'notes': '防腐蚀垫片'
            }
        },
        {
            'name': '小齿轮',
            'bbox': (595, 95, 110, 110),
            'info': {
                'name': '小齿轮',
                'type': '传动件',
                'specifications': '模数1.5，齿数20',
                'material': '合金钢',
                'supplier': 'GHI齿轮厂',
                'notes': '精密加工齿轮'
            }
        }
    ]
    
    # 学习每个工件
    learned_ids = []
    for workpiece in workpieces:
        print(f"📚 学习工件: {workpiece['name']}")
        workpiece_id = learner.learn_workpiece(img, workpiece['bbox'], workpiece['info'])
        if workpiece_id:
            learned_ids.append(workpiece_id)
            print(f"   ✅ 学习成功，ID: {workpiece_id}")
        else:
            print(f"   ❌ 学习失败")
    
    print(f"\n📊 总共学习了 {len(learned_ids)} 个工件")
    
    # 训练模型
    print("\n🤖 开始训练识别模型...")
    if learner.train_model():
        print("✅ 模型训练成功")
    else:
        print("❌ 模型训练失败")
        return False
    
    return True

def demo_workpiece_recognition():
    """演示工件识别"""
    print("\n🔍 开始工件识别演示...")
    
    # 创建学习器并加载模型
    learner = CustomWorkpieceLearner()
    if not learner.load_model():
        print("❌ 模型加载失败，请先运行学习演示")
        return
    
    # 加载测试图像
    if not os.path.exists("sample_workpieces.jpg"):
        print("❌ 测试图像不存在，请先运行学习演示")
        return
    
    img = cv2.imread("sample_workpieces.jpg")
    
    # 测试识别下半部分的工件
    test_regions = [
        (150, 350, 80, 100),  # 螺丝
        (360, 360, 80, 80),   # 螺母
    ]
    
    print("🎯 测试识别结果:")
    for i, bbox in enumerate(test_regions):
        print(f"\n--- 测试区域 {i+1} ---")
        result = learner.recognize_workpiece(img, bbox)
        
        if result['success']:
            print(f"✅ 识别成功!")
            print(f"   工件名称: {result['workpiece_name']}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   位置: ({result['center_x']}, {result['center_y']})")
            
            if result['workpiece_info']:
                info = result['workpiece_info']
                print(f"   详细信息:")
                print(f"     类型: {info.get('type', 'N/A')}")
                print(f"     规格: {info.get('specifications', 'N/A')}")
                print(f"     材料: {info.get('material', 'N/A')}")
                print(f"     供应商: {info.get('supplier', 'N/A')}")
                print(f"     备注: {info.get('notes', 'N/A')}")
        else:
            print(f"❌ 识别失败: {result.get('error', '未知错误')}")

def main():
    """主函数"""
    print("🔧 自定义工件识别系统演示")
    print("=" * 50)
    
    # 检查是否已有训练好的模型
    learner = CustomWorkpieceLearner()
    model_info = learner.get_model_info()
    
    if model_info['is_trained'] or model_info['model_exists']:
        print("📋 发现已有模型，直接进行识别演示")
        demo_workpiece_recognition()
    else:
        print("📋 没有发现训练好的模型，开始完整演示")
        if demo_workpiece_learning():
            demo_workpiece_recognition()
    
    print("\n" + "=" * 50)
    print("💡 现在您可以:")
    print("1. 运行 python workpiece_learning_gui.py 来训练您自己的工件")
    print("2. 运行 python vision_demo.py 来使用ROI功能识别工件")
    print("3. 在vision_demo.py中选择'自定义学习'算法来识别您训练的工件")

if __name__ == "__main__":
    main()
