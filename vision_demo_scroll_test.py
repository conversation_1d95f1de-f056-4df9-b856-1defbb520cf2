#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vision Demo GUI 滚动功能测试版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading

class VisionDemoScrollTest:
    """视觉检测演示GUI界面 - 滚动功能测试版"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DobotVisionStudio 工件识别演示 - 滚动测试版")
        self.root.geometry("1400x900")

        # 简化的变量初始化
        self.current_image = None
        self.detection_results = []
        
        # ROI选择相关
        self.roi_start_point = None
        self.roi_end_point = None
        self.roi_rect = None
        self.is_drawing_roi = False
        self.roi_enabled = False
        self.roi_locked = False
        self.stable_roi = None
        self.roi_confidence_threshold = 0.6

        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板容器
        control_container = ttk.LabelFrame(main_frame, text="控制面板", width=300)
        control_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_container.pack_propagate(False)
        
        # 创建可滚动的控制面板
        self.setup_scrollable_control_panel(control_container)
        
        # 图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图像显示")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 设置图像显示
        self.setup_image_display(image_frame)
        
    def setup_scrollable_control_panel(self, parent):
        """设置可滚动的控制面板"""
        # 创建Canvas和Scrollbar
        self.control_canvas = tk.Canvas(parent, highlightthickness=0)
        self.control_scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.control_canvas.yview)
        self.control_canvas.configure(yscrollcommand=self.control_scrollbar.set)
        
        # 创建可滚动的框架
        self.scrollable_frame = ttk.Frame(self.control_canvas)
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
        )
        
        # 将框架添加到Canvas
        self.canvas_frame = self.control_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # 布局Canvas和Scrollbar
        self.control_canvas.pack(side="left", fill="both", expand=True)
        self.control_scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        self.bind_mousewheel_to_canvas()
        
        # 绑定Canvas大小变化事件，确保内容框架宽度适应
        self.control_canvas.bind('<Configure>', self.on_canvas_configure)
        
        # 设置控制面板内容
        self.setup_control_panel(self.scrollable_frame)
        
    def bind_mousewheel_to_canvas(self):
        """绑定鼠标滚轮事件到Canvas"""
        def _on_mousewheel(event):
            # 检查鼠标是否在控制面板区域内
            if self.control_canvas.winfo_containing(event.x_root, event.y_root) == self.control_canvas:
                self.control_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        def _bind_to_mousewheel(event):
            self.control_canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        def _unbind_from_mousewheel(event):
            self.control_canvas.unbind_all("<MouseWheel>")
        
        # 绑定鼠标进入和离开事件
        self.control_canvas.bind('<Enter>', _bind_to_mousewheel)
        self.control_canvas.bind('<Leave>', _unbind_from_mousewheel)
        
    def on_canvas_configure(self, event):
        """Canvas大小变化时调整内容框架宽度"""
        canvas_width = event.width
        self.control_canvas.itemconfig(self.canvas_frame, width=canvas_width)
        
    def setup_control_panel(self, parent):
        """设置控制面板"""
        # 文件操作
        file_frame = ttk.LabelFrame(parent, text="文件操作")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(file_frame, text="加载图像", command=self.load_image).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="加载模板", command=self.load_template).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="保存结果", command=self.save_results).pack(fill=tk.X, pady=2)
        
        # 检测算法选择
        algo_frame = ttk.LabelFrame(parent, text="检测算法")
        algo_frame.pack(fill=tk.X, pady=5)

        self.algo_var = tk.StringVar(value="custom")
        ttk.Radiobutton(algo_frame, text="圆形检测", variable=self.algo_var, value="circle").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="模板匹配", variable=self.algo_var, value="template").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="边缘特征", variable=self.algo_var, value="edge").pack(anchor=tk.W)

        # 突出显示自定义学习选项
        custom_frame = ttk.Frame(algo_frame)
        custom_frame.pack(fill=tk.X, pady=2)
        ttk.Radiobutton(custom_frame, text="🎯 自定义工件识别", variable=self.algo_var, value="custom").pack(anchor=tk.W)
        ttk.Label(custom_frame, text="   (识别您训练的工件)", font=("Arial", 8),
                 foreground="blue").pack(anchor=tk.W)
        
        # 参数设置
        param_frame = ttk.LabelFrame(parent, text="参数设置")
        param_frame.pack(fill=tk.X, pady=5)
        
        # 圆形检测参数
        ttk.Label(param_frame, text="最小半径:").pack(anchor=tk.W)
        self.min_radius_var = tk.IntVar(value=10)
        ttk.Scale(param_frame, from_=5, to=50, variable=self.min_radius_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="最大半径:").pack(anchor=tk.W)
        self.max_radius_var = tk.IntVar(value=200)
        ttk.Scale(param_frame, from_=50, to=500, variable=self.max_radius_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # 模板匹配参数
        ttk.Label(param_frame, text="匹配阈值:").pack(anchor=tk.W)
        self.match_threshold_var = tk.DoubleVar(value=0.7)
        ttk.Scale(param_frame, from_=0.1, to=1.0, variable=self.match_threshold_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # ROI选择控制
        roi_frame = ttk.LabelFrame(parent, text="检测区域选择")
        roi_frame.pack(fill=tk.X, pady=5)

        self.roi_enabled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(roi_frame, text="启用区域选择",
                       variable=self.roi_enabled_var,
                       command=self.toggle_roi_mode).pack(anchor=tk.W, pady=2)

        ttk.Button(roi_frame, text="绘制检测区域", command=self.start_roi_selection).pack(fill=tk.X, pady=1)

        # ROI控制按钮
        roi_control_frame = ttk.Frame(roi_frame)
        roi_control_frame.pack(fill=tk.X, pady=1)
        ttk.Button(roi_control_frame, text="锁定区域", command=self.lock_roi).pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
        ttk.Button(roi_control_frame, text="清除区域", command=self.clear_roi).pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)

        self.roi_status_var = tk.StringVar(value="未选择区域")
        ttk.Label(roi_frame, textvariable=self.roi_status_var, font=("Arial", 8)).pack(pady=1)

        # 识别参数调整
        param_adj_frame = ttk.LabelFrame(roi_frame, text="识别参数")
        param_adj_frame.pack(fill=tk.X, pady=2)

        ttk.Label(param_adj_frame, text="置信度阈值:", font=("Arial", 8)).pack(anchor=tk.W)
        self.confidence_threshold_var = tk.DoubleVar(value=0.6)
        confidence_scale = ttk.Scale(param_adj_frame, from_=0.1, to=1.0,
                                   variable=self.confidence_threshold_var,
                                   orient=tk.HORIZONTAL)
        confidence_scale.pack(fill=tk.X, padx=2)

        self.confidence_label_var = tk.StringVar(value="0.60")
        ttk.Label(param_adj_frame, textvariable=self.confidence_label_var,
                 font=("Arial", 8)).pack()

        # 绑定置信度更新
        confidence_scale.configure(command=self.update_confidence_display)

        # 执行按钮
        action_frame = ttk.LabelFrame(parent, text="执行操作")
        action_frame.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame, text="开始检测", command=self.start_detection).pack(fill=tk.X, pady=2)
        ttk.Button(action_frame, text="清除结果", command=self.clear_results).pack(fill=tk.X, pady=2)

        # 测试功能
        test_frame = ttk.LabelFrame(parent, text="测试功能")
        test_frame.pack(fill=tk.X, pady=5)

        ttk.Button(test_frame, text="测试图像显示", command=self.test_image_display).pack(fill=tk.X, pady=1)
        ttk.Button(test_frame, text="创建测试图像", command=self.create_test_image).pack(fill=tk.X, pady=1)

        # 自定义学习功能
        custom_frame = ttk.LabelFrame(parent, text="🎯 自定义工件识别")
        custom_frame.pack(fill=tk.X, pady=5)

        # 添加使用说明
        help_label = ttk.Label(custom_frame, text="识别您训练的工件:", font=("Arial", 9, "bold"),
                              foreground="blue")
        help_label.pack(anchor=tk.W, pady=2)

        steps_text = """1. 打开学习界面训练工件
2. 选择'自定义工件识别'算法
3. 使用ROI框选工件区域
4. 开始检测获得工件信息"""
        steps_label = ttk.Label(custom_frame, text=steps_text, font=("Arial", 8),
                               justify=tk.LEFT)
        steps_label.pack(anchor=tk.W, pady=2)

        ttk.Button(custom_frame, text="🚀 打开学习界面", command=self.open_learning_gui).pack(fill=tk.X, pady=1)
        ttk.Button(custom_frame, text="🤖 训练模型", command=self.train_custom_model).pack(fill=tk.X, pady=1)

        self.custom_model_status_var = tk.StringVar(value="模型未训练")
        status_label = ttk.Label(custom_frame, textvariable=self.custom_model_status_var,
                                font=("Arial", 8))
        status_label.pack(pady=1)
        
        # 硬件连接控制
        hardware_frame = ttk.LabelFrame(parent, text="视觉硬件连接")
        hardware_frame.pack(fill=tk.X, pady=5)

        self.hardware_status_var = tk.StringVar(value="等待连接")
        ttk.Label(hardware_frame, textvariable=self.hardware_status_var).pack()

        # 自动检测开关
        self.auto_detect_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(hardware_frame, text="自动检测硬件图像",
                       variable=self.auto_detect_var).pack(anchor=tk.W, pady=2)

        ttk.Button(hardware_frame, text="触发拍照", command=self.trigger_hardware_capture).pack(fill=tk.X, pady=2)
        ttk.Button(hardware_frame, text="重连硬件", command=self.reconnect_hardware).pack(fill=tk.X, pady=2)

        # TCP服务器控制
        tcp_frame = ttk.LabelFrame(parent, text="TCP服务器")
        tcp_frame.pack(fill=tk.X, pady=5)

        self.tcp_status_var = tk.StringVar(value="未启动")
        ttk.Label(tcp_frame, textvariable=self.tcp_status_var).pack()

        ttk.Button(tcp_frame, text="启动服务器", command=self.start_tcp_server).pack(fill=tk.X, pady=2)
        ttk.Button(tcp_frame, text="停止服务器", command=self.stop_tcp_server).pack(fill=tk.X, pady=2)
        
        # 结果显示
        result_frame = ttk.LabelFrame(parent, text="检测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.result_text = tk.Text(result_frame, height=10, width=30)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加更多测试内容来验证滚动功能
        for i in range(5):
            extra_frame = ttk.LabelFrame(parent, text=f"额外功能 {i+1}")
            extra_frame.pack(fill=tk.X, pady=5)
            
            for j in range(3):
                ttk.Button(extra_frame, text=f"测试按钮 {j+1}", 
                          command=lambda i=i, j=j: self.test_button_click(i, j)).pack(fill=tk.X, pady=1)
        
    def setup_image_display(self, parent):
        """设置图像显示区域"""
        # 图像显示画布
        self.canvas = tk.Canvas(parent, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 状态栏
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_var = tk.StringVar(value="就绪 - 滚动测试版")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        self.coord_var = tk.StringVar(value="坐标: (0, 0)")
        ttk.Label(status_frame, textvariable=self.coord_var).pack(side=tk.RIGHT)
        
    # 简化的方法实现
    def load_image(self):
        messagebox.showinfo("测试", "加载图像功能 - 滚动测试版")
        
    def load_template(self):
        messagebox.showinfo("测试", "加载模板功能 - 滚动测试版")
        
    def save_results(self):
        messagebox.showinfo("测试", "保存结果功能 - 滚动测试版")
        
    def toggle_roi_mode(self):
        if self.roi_enabled_var.get():
            self.roi_status_var.set("ROI模式已启用")
        else:
            self.roi_status_var.set("ROI模式已关闭")
            
    def start_roi_selection(self):
        messagebox.showinfo("测试", "ROI选择功能 - 滚动测试版")
        
    def lock_roi(self):
        messagebox.showinfo("测试", "锁定ROI功能 - 滚动测试版")
        
    def clear_roi(self):
        messagebox.showinfo("测试", "清除ROI功能 - 滚动测试版")
        
    def update_confidence_display(self, value):
        confidence = float(value)
        self.confidence_label_var.set(f"{confidence:.2f}")
        
    def start_detection(self):
        messagebox.showinfo("测试", "开始检测功能 - 滚动测试版")
        
    def clear_results(self):
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "结果已清除 - 滚动测试版\n")
        
    def test_image_display(self):
        messagebox.showinfo("测试", "测试图像显示功能 - 滚动测试版")
        
    def create_test_image(self):
        messagebox.showinfo("测试", "创建测试图像功能 - 滚动测试版")
        
    def open_learning_gui(self):
        messagebox.showinfo("测试", "打开学习界面功能 - 滚动测试版")
        
    def train_custom_model(self):
        messagebox.showinfo("测试", "训练模型功能 - 滚动测试版")
        
    def trigger_hardware_capture(self):
        messagebox.showinfo("测试", "触发拍照功能 - 滚动测试版")
        
    def reconnect_hardware(self):
        messagebox.showinfo("测试", "重连硬件功能 - 滚动测试版")
        
    def start_tcp_server(self):
        messagebox.showinfo("测试", "启动TCP服务器功能 - 滚动测试版")
        
    def stop_tcp_server(self):
        messagebox.showinfo("测试", "停止TCP服务器功能 - 滚动测试版")
        
    def test_button_click(self, i, j):
        message = f"点击了额外功能 {i+1} 的按钮 {j+1}"
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VisionDemoScrollTest()
    app.run()
